/**
 * Constants for Ascension effect
 */
const LEVITATION_DURATION = 200; // Duration in ticks (10 seconds)
const LEVITATION_AMPLIFIER = 7; // Strength of the levitation (higher = faster ascent)
/**
 * Event 69: Ascension - Effects the player with a strong Levitation for 10 seconds
 * Launches the player high into the air with levitation effect
 *
 * @param player - The player who triggered the event
 */
export function event69(player) {
    try {
        const dimension = player.dimension;
        const playerPos = player.location;
        // Play initial sound and particle effect
        dimension.playSound('mob.wither.shoot', playerPos, { pitch: 1.5, volume: 0.8 });
        dimension.spawnParticle('minecraft:enchanting_table_particle', playerPos);
        // Apply the levitation effect (strong, 10 seconds)
        player.addEffect('levitation', LEVITATION_DURATION, {
            amplifier: LEVITATION_AMPLIFIER,
            showParticles: true
        });
        // Add a single dramatic particle effect
        dimension.spawnParticle('minecraft:huge_explosion_emitter', playerPos);
    }
    catch (error) {
    }
    return;
}
