import { Block, BlockComponentTypes, BlockInventoryComponent, ItemStack } from '@minecraft/server';
import { getRandomInt } from './rng';

/**
 * Fills a chest block with random items from a provided array
 * @param chest - The target chest Block to fill
 * @param items - Array of ItemStacks to randomly select from (may be modified if decrementAmount is true)
 * @param randomSlot - Whether to fill random slots or all slots
 * @param decrementAmount - Whether to decrement item amounts as they are used (default: true).
 *                         If false, puts the entire ItemStack in a slot and considers it exhausted.
 * @returns void
 *
 * @remarks
 * This function:
 * - Attempts to access the chest's inventory container
 * - Iterates through slots in the container (random or sequential)
 * - Places items from the provided array
 * - If decrementAmount is true:
 *   - Decrements items' amount as they are used
 *   - Removes items from the pool when their stack is exhausted
 *   - Places one item at a time
 * - If decrementAmount is false:
 *   - Places the entire ItemStack in the slot
 *   - Considers the ItemStack exhausted after placing it once
 *   - Removes it from the pool and moves to next item
 * - Attempts to stack items in existing slots where possible (only when decrementAmount is true)
 * - Finds alternative empty slots if stacking fails
 * - Stops filling when no items remain
 *
 * @throws May log warnings to console if chest filling fails
 */
export function chestFiller(
  chest: Block,
  items: ItemStack[],
  randomSlot: boolean,
  decrementAmount: boolean = true
): void {
  try {
    const container = (chest.getComponent(BlockComponentTypes.Inventory) as BlockInventoryComponent)
      .container;
    if (!container) return;
    const containerSize = container.size;

    // Create a working copy of the items array
    const availableItems: ItemStack[] = [...items];

    // Helper function to get and remove a random item
    const getRandomAvailableItem = (): ItemStack | undefined => {
      if (availableItems.length === 0) return undefined;
      const index = getRandomInt(0, availableItems.length - 1);
      const selectedItem = availableItems[index];

      if (!selectedItem) return undefined;

      if (decrementAmount) {
        // When decrementing, take one item at a time
        if (selectedItem.amount > 1) {
          selectedItem.amount--;
        } else {
          // Remove the item if its stack is exhausted
          availableItems.splice(index, 1);
        }
        return new ItemStack(selectedItem.typeId, 1);
      } else {
        // When not decrementing, use the entire stack and consider it exhausted
        availableItems.splice(index, 1); // Remove the item immediately
        return new ItemStack(selectedItem.typeId, selectedItem.amount);
      }
    };

    // Helper function to find an empty slot
    const findEmptySlot = (startIndex: number): number | undefined => {
      for (let i = 0; i < containerSize; i++) {
        const index = (startIndex + i) % containerSize;
        const slot = container.getSlot(index);
        if (!slot?.hasItem()) {
          return index;
        }
      }
      return undefined;
    };

    // Helper function to try placing item in a slot
    const tryPlaceItem = (slotIndex: number, item: ItemStack): boolean => {
      const slot = container.getSlot(slotIndex);
      if (!slot) return false;

      try {
        if (!slot.hasItem()) {
          // Empty slot - place item directly
          slot.setItem(item);
          return true;
        } else if (decrementAmount) {
          // Only attempt stacking if we're decrementing amounts
          const existingItem = slot.getItem();
          if (existingItem?.typeId === item.typeId) {
            // Try to stack with existing item
            const newAmount = existingItem.amount + item.amount;
            const newStack = new ItemStack(item.typeId, newAmount);
            slot.setItem(newStack);
            return true;
          }
        }
      } catch {
        // Stacking failed (possibly unstackable item or max stack reached)
        // Find another empty slot
        const emptySlot = findEmptySlot(0);
        if (emptySlot !== undefined) {
          const newSlot = container.getSlot(emptySlot);
          if (newSlot) {
            newSlot.setItem(item);
            return true;
          }
        }
      }
      return false;
    };

    // Fill slots based on random or sequential pattern
    if (randomSlot) {
      // Since we want to fill slots until either we run out of items or slots
      let attempts = 0;
      while (availableItems.length > 0 && attempts < containerSize) {
        const item = getRandomAvailableItem();
        if (item) {
          const randomSlotIndex = getRandomInt(0, containerSize - 1);
          if (!tryPlaceItem(randomSlotIndex, item)) {
            // If placement fails, try to find any empty slot
            const emptySlot = findEmptySlot(0);
            if (emptySlot !== undefined) {
              tryPlaceItem(emptySlot, item);
            }
          }
        }
        attempts++;
      }
    } else {
      for (let i = 0; i < containerSize && availableItems.length > 0; i++) {
        const item = getRandomAvailableItem();
        if (item) {
          if (!tryPlaceItem(i, item)) {
            // If placement fails, try to find any empty slot
            const emptySlot = findEmptySlot(i + 1);
            if (emptySlot !== undefined) {
              tryPlaceItem(emptySlot, item);
            }
          }
        }
      }
    }
    return;
  } catch (error) {
    return;
  }
}
