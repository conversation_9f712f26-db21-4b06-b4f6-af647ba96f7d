import { Player, system } from '@minecraft/server';

/**
 * Event 13: Enchanted Arsenal
 * Spawns enchanted netherite armor pieces
 * @param player - The player who triggered the event
 */
export async function event13(player: Player): Promise<void> {
  try {
    const dimension = player.dimension;
    const spawnLocation = player.location;

    // Wait for 1 tick to ensure the armor stand is fully initialized
    await system.waitTicks(1);

    // Spawn the netherite armor set using loot table
    dimension.runCommand(
      `loot spawn ${spawnLocation.x} ${spawnLocation.y} ${spawnLocation.z} loot "ptd/lm_hc/event13"`
    );

    // Add particles and sound effects
    dimension.playSound('random.totem', spawnLocation, { volume: 0.4 });
    for (let i = 0; i < 10; i++) {
      dimension.spawnParticle('minecraft:totem_particle', spawnLocation);
    }
  } catch (error) {
  }
  return;
}
