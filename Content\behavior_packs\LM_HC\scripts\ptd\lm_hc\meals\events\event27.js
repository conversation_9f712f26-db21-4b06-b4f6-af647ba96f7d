import { EntityComponentTypes, EquipmentSlot, ItemStack } from '@minecraft/server';
/**
 * Event 27: Mining Away = <PERSON>iine <PERSON>!
 * Gives the player a miner's helmet that emits light
 * @param player The player who triggered the event
 */
export function event27(player) {
    try {
        // Create miner's helmet with light emission
        const minersHelmet = new ItemStack('ptd_lmhc:miners_helmet');
        // Get player location and spawn item
        const location = player.location;
        player.dimension.spawnItem(minersHelmet, location);
        // Play mining-themed effects
        const particleLocations = [
            { ...location, y: location.y + 0.5 },
            { ...location, y: location.y + 1.0 },
            { ...location, y: location.y + 1.5 }
        ];
        // Spawn multiple particles for dramatic effect
        particleLocations.forEach((pos) => {
            player.dimension.spawnParticle('minecraft:totem_particle', pos);
        });
        // Play mining sounds
        player.playSound('random.pop');
        player.playSound('random.levelup');
    }
    catch (error) {
    }
    return;
}
/**
 * Handles the miner helmet lighting mechanics by placing a single light block at player's head
 * and removing old light blocks within a specific radius as the player moves
 * @param player The player to check for miner's helmet
 */
export function minerHelmetMechanics(player) {
    try {
        // Check if player exists and is valid
        if (!player || !player) {
            return;
        }
        // Check if player is wearing miner's helmet
        const armorInv = player.getComponent(EntityComponentTypes.Equippable);
        const helmet = armorInv.getEquipment(EquipmentSlot.Head);
        const isWearingMinersHelmet = helmet?.type.id === 'ptd_lmhc:miners_helmet';
        // Get cleanup ticks
        let cleanupTicks = player.getDynamicProperty('minerHelmetCleanupTicks') ?? 0;
        // Handle cleanup when helmet is removed
        if (!isWearingMinersHelmet) {
            cleanupTicks = Math.min(cleanupTicks + 1, 5);
            player.setDynamicProperty('minerHelmetCleanupTicks', cleanupTicks);
            // Exit early if cleanup period is over
            if (cleanupTicks === 5) {
                return;
            }
        }
        else {
            // Reset cleanup ticks when wearing helmet
            if (cleanupTicks !== 0) {
                player.setDynamicProperty('minerHelmetCleanupTicks', 0);
            }
        }
        // Get player's head position
        const headPos = player.getHeadLocation();
        const headBlockPos = {
            x: Math.floor(headPos.x),
            y: Math.floor(headPos.y),
            z: Math.floor(headPos.z)
        };
        // Place light block at head if wearing helmet
        if (isWearingMinersHelmet) {
            const headBlock = player.dimension.getBlock(headBlockPos);
            if (headBlock &&
                (headBlock.typeId === 'minecraft:air' || headBlock.typeId === 'minecraft:light_block_15')) {
                headBlock.setType('minecraft:light_block_15');
            }
            // Clear old light blocks in 2-8 block range for realistic lighting
            const cleanupMin = {
                x: headBlockPos.x - 8,
                y: headBlockPos.y - 8,
                z: headBlockPos.z - 8
            };
            const cleanupMax = {
                x: headBlockPos.x + 8,
                y: headBlockPos.y + 8,
                z: headBlockPos.z + 8
            };
            for (let x = cleanupMin.x; x <= cleanupMax.x; x++) {
                for (let y = cleanupMin.y; y <= cleanupMax.y; y++) {
                    for (let z = cleanupMin.z; z <= cleanupMax.z; z++) {
                        // Skip the current head position
                        if (x === headBlockPos.x && y === headBlockPos.y && z === headBlockPos.z) {
                            continue;
                        }
                        const blockPos = { x, y, z };
                        const dist = Math.sqrt(Math.pow(x - headBlockPos.x, 2) +
                            Math.pow(y - headBlockPos.y, 2) +
                            Math.pow(z - headBlockPos.z, 2));
                        // Only remove light blocks between 2 and 8 blocks away
                        if (dist >= 2 && dist <= 8) {
                            const block = player.dimension.getBlock(blockPos);
                            if (block?.typeId === 'minecraft:light_block_15') {
                                block.setType('minecraft:air');
                            }
                        }
                    }
                }
            }
        }
        else {
            // When helmet is removed and in cleanup period, clear entire 8x8x8 area
            const cleanupMin = {
                x: headBlockPos.x - 4,
                y: headBlockPos.y - 4,
                z: headBlockPos.z - 4
            };
            const cleanupMax = {
                x: headBlockPos.x + 4,
                y: headBlockPos.y + 4,
                z: headBlockPos.z + 4
            };
            for (let x = cleanupMin.x; x <= cleanupMax.x; x++) {
                for (let y = cleanupMin.y; y <= cleanupMax.y; y++) {
                    for (let z = cleanupMin.z; z <= cleanupMax.z; z++) {
                        const blockPos = { x, y, z };
                        const block = player.dimension.getBlock(blockPos);
                        if (block?.typeId === 'minecraft:light_block_15') {
                            block.setType('minecraft:air');
                        }
                    }
                }
            }
        }
    }
    catch (error) {
    }
    return;
}
