import { Player, EntityComponentTypes, EntityInventoryComponent } from '@minecraft/server';
import { getRandomInt } from '../../utilities/rng';

/**
 * Event 58: Item Vanish
 * Removes 1 to 5 item types from the player's inventory
 * If an item is stackable, the entire stack is removed
 * @param player The player who triggered the event
 */
export function event58(player: Player): void {
  try {
    // Get the player's inventory component
    const inventory = player.getComponent(EntityComponentTypes.Inventory) as EntityInventoryComponent;
    if (!inventory || !inventory.container) {
      return;
    }

    // Determine number of item types to remove (1-5)
    const numItemsToRemove = getRandomInt(1, 5);

    // Track all non-empty slots in inventory
    const occupiedSlots: number[] = [];
    for (let i = 0; i < inventory.container.size; i++) {
      const itemStack = inventory.container.getItem(i);
      if (itemStack) {
        occupiedSlots.push(i);
      }
    }

    // If inventory is empty, exit early
    if (occupiedSlots.length === 0) {
      player.sendMessage('§cYour inventory is already empty!');
      return;
    }

    // Track items removed for messaging
    const removedItems: { name: string; amount: number }[] = [];

    // Remove up to numItemsToRemove different item types
    for (let i = 0; i < Math.min(numItemsToRemove, occupiedSlots.length); i++) {
      // Pick a random slot with an item
      const randomIndex = getRandomInt(0, occupiedSlots.length - 1);

      // Ensure the index is valid
      if (randomIndex >= 0 && randomIndex < occupiedSlots.length) {
        // Using a definite assignment assertion to tell TypeScript this value is not undefined
        const slotIndex = occupiedSlots[randomIndex]!;

        // Get the item stack in that slot
        const itemStack = inventory.container.getItem(slotIndex);
        if (itemStack) {
          // Track info about the removed item
          const itemTypeId = itemStack.typeId;
          let itemName = '';

          // Extract the item name from the typeId by removing any namespace prefix
          const colonIndex = itemTypeId.indexOf(':');
          if (colonIndex !== -1) {
            // Get everything after the colon
            itemName = itemTypeId.substring(colonIndex + 1).replace(/_/g, ' ');
          } else {
            // Fallback if no namespace prefix found
            itemName = itemTypeId.replace(/_/g, ' ');
          }
          const itemAmount = itemStack.amount;
          removedItems.push({
            name: itemName,
            amount: itemAmount
          });

          // Remove the entire stack by setting the slot to undefined
          inventory.container.setItem(slotIndex, undefined);
        }

        // Remove this slot from consideration for future removals
        occupiedSlots.splice(randomIndex, 1);
      }
    }

    // Display information about all removed items
    if (removedItems.length > 0) {
      player.sendMessage('§cThe following items were removed from your inventory:');
      for (const item of removedItems) {
        player.sendMessage(`§c- ${item.amount}x ${item.name}`);
      }

      // Play a sound effect
      player.dimension.playSound('mob.endermen.portal', player.location);
    } else {
      player.sendMessage('§cNo items were removed from your inventory.');
    }
  } catch (error) {
  }
}
