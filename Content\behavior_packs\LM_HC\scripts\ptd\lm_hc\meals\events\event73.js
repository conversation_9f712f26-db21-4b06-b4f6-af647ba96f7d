import { EntityComponentTypes, system } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';
// Constants for Divine Escape
export const DIVINE_ESCAPE_TAG = 'divine_escape';
const TELEPORT_BASE_DISTANCE = 30;
const TELEPORT_EXTRA_DISTANCE = 8;
const TELEPORT_Y_OFFSET = 0;
export const HEALTH_THRESHOLD = 6; // When health is 6 (3 hearts) or less
const MAX_TELEPORT_ATTEMPTS = 40; // Maximum attempts to find a safe location
/**
 * Event 73: Divine Escape - When the player's hearts reach 3, player is teleported 30 blocks away
 * This event tags the player with 'divine_escape' and sets up a listener to monitor their health
 *
 * @param player - The player who triggered the event
 */
export function event73(player) {
    try {
        // Add the divine_escape tag to the player
        player.addTag(DIVINE_ESCAPE_TAG);
        // Visual effects for activation
        const dimension = player.dimension;
        dimension.playSound('mob.endermen.portal', player.location, {
            volume: 0.5,
            pitch: 1.2
        });
    }
    catch (error) {
    }
    return;
}
/**
 * Handles Divine Escape mechanics when a player with the divine_escape tag is damaged
 * Checks if their health is below the threshold and teleports them if needed
 *
 * @param player - The player who has taken damage
 */
export async function handleDivineEscapeDamage(player) {
    try {
        if (!player || !player || !player.hasTag(DIVINE_ESCAPE_TAG))
            return;
        // Wait 1 tick to check health after damage is applied
        await system.waitTicks(1);
        // Entity might have become invalid after waiting
        if (!player)
            return;
        // Get the player's current health
        const healthComponent = player.getComponent(EntityComponentTypes.Health);
        if (!healthComponent)
            return;
        const currentHealth = healthComponent.currentValue;
        // If health is at or below threshold, trigger teleportation
        if (currentHealth <= HEALTH_THRESHOLD) {
            teleportPlayerToSafety(player);
        }
    }
    catch (error) {
    }
}
/**
 * Checks if a block is solid (can be stood on safely)
 *
 * @param dimension - The dimension to check in
 * @param location - The location to check
 * @returns true if the block is solid, false otherwise
 */
function isSolidBlock(dimension, location) {
    try {
        const block = dimension.getBlock(location);
        if (!block)
            return false;
        // List of block types that are not solid/safe to stand on
        const nonSolidBlocks = [
            'minecraft:air',
            'minecraft:water',
            'minecraft:flowing_water',
            'minecraft:lava',
            'minecraft:flowing_lava',
            'minecraft:fire',
            'minecraft:soul_fire',
            'minecraft:cobweb',
            'minecraft:seagrass',
            'minecraft:tall_seagrass',
            'minecraft:kelp',
            'minecraft:bamboo',
            'minecraft:scaffolding',
            // Plants and vegetation
            'minecraft:short_grass',
            'minecraft:tall_grass',
            'minecraft:fern',
            'minecraft:large_fern',
            'minecraft:flowers',
            'minecraft:dandelion',
            'minecraft:poppy',
            'minecraft:blue_orchid',
            'minecraft:allium',
            'minecraft:azure_bluet',
            'minecraft:red_tulip',
            'minecraft:orange_tulip',
            'minecraft:white_tulip',
            'minecraft:pink_tulip',
            'minecraft:oxeye_daisy',
            'minecraft:cornflower',
            'minecraft:lily_of_the_valley',
            'minecraft:wither_rose',
            'minecraft:sunflower',
            'minecraft:lilac',
            'minecraft:rose_bush',
            'minecraft:peony',
            'minecraft:sweet_berry_bush',
            'minecraft:dead_bush',
            'minecraft:vine',
            'minecraft:weeping_vines',
            'minecraft:twisting_vines',
            'minecraft:glow_lichen',
            'minecraft:crimson_roots',
            'minecraft:warped_roots',
            'minecraft:nether_sprouts',
            'minecraft:hanging_roots',
            // Liquids and bubbles
            'minecraft:bubble_column',
            // Decorative and small items
            'minecraft:torch',
            'minecraft:soul_torch',
            'minecraft:redstone_torch',
            'minecraft:lantern',
            'minecraft:soul_lantern',
            'minecraft:candle',
            'minecraft:end_rod',
            'minecraft:lever',
            'minecraft:ladder',
            'minecraft:snow',
            'minecraft:flower_pot',
            // Partial blocks
            'minecraft:brewing_stand',
            'minecraft:enchanting_table',
            'minecraft:cake',
            'minecraft:chain',
            'minecraft:pointed_dripstone',
            // Slippery blocks
            'minecraft:slime_block',
            'minecraft:honey_block',
            'minecraft:mud',
            // Trap blocks
            'minecraft:magma_block',
            'minecraft:powder_snow',
            // Redstone components
            'minecraft:redstone_wire',
            'minecraft:comparator',
            'minecraft:repeater',
            'minecraft:daylight_detector',
            'minecraft:sculk_sensor',
            'minecraft:calibrated_sculk_sensor',
            'minecraft:pressure_plate',
            'minecraft:tripwire',
            'minecraft:tripwire_hook',
            'minecraft:button',
            'minecraft:rail',
            'minecraft:powered_rail',
            'minecraft:detector_rail',
            'minecraft:activator_rail',
            // Carpets and thin blocks
            'minecraft:carpet',
            'minecraft:moss_carpet',
            // Nether and End features
            'minecraft:nether_wart',
            'minecraft:chorus_flower',
            'minecraft:chorus_plant',
            // Cave and underwater features
            'minecraft:amethyst_cluster',
            'minecraft:small_amethyst_bud',
            'minecraft:medium_amethyst_bud',
            'minecraft:large_amethyst_bud',
            'minecraft:glow_berries',
            'minecraft:cave_vines',
            'minecraft:spore_blossom',
            'minecraft:dripleaf',
            'minecraft:big_dripleaf',
            'minecraft:small_dripleaf'
        ];
        return !nonSolidBlocks.includes(block.type.id);
    }
    catch (error) {
        return false;
    }
}
/**
 * Teleports the player to a safe location and removes the divine_escape tag
 *
 * @param player - The player to teleport
 */
async function teleportPlayerToSafety(player) {
    try {
        if (!player || !player)
            return;
        const dimension = player.dimension;
        const playerPos = player.location;
        let safeLocation = null;
        // Try multiple times to find a location with a solid block below
        for (let attempts = 0; attempts < MAX_TELEPORT_ATTEMPTS; attempts++) {
            // Find a safe location nearby with air blocks
            const potentialLocation = getRandomLocation(playerPos, dimension, TELEPORT_BASE_DISTANCE, TELEPORT_EXTRA_DISTANCE, TELEPORT_Y_OFFSET, true // Check for air blocks
            );
            if (!potentialLocation)
                continue;
            // Check if the block below is solid
            const blockBelowPos = {
                x: potentialLocation.x,
                y: potentialLocation.y - 1,
                z: potentialLocation.z
            };
            if (isSolidBlock(dimension, blockBelowPos)) {
                safeLocation = potentialLocation;
                player.sendMessage('§2You have used your Divine Escape ability!§r');
                break;
            }
        }
        if (!safeLocation) {
            `Could not find safe location with solid ground for Divine Escape after ${MAX_TELEPORT_ATTEMPTS} attempts`;
            ;
            // Remove the tag even if we couldn't find a safe location
            player.removeTag(DIVINE_ESCAPE_TAG);
            return;
        }
        // Teleport the player
        player.teleport(safeLocation);
        // Remove the tag to prevent multiple teleportations
        player.removeTag(DIVINE_ESCAPE_TAG);
        // Effects before teleportation
        dimension.playSound('mob.endermen.portal', safeLocation);
        await system.waitTicks(1);
        // Effects after teleportation
        dimension.playSound('mob.endermen.portal', safeLocation);
    }
    catch (error) {
        // Try to remove the tag even if teleport failed
        if (player && player) {
            player.removeTag(DIVINE_ESCAPE_TAG);
        }
    }
}
