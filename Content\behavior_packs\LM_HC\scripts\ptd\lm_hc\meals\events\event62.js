import { getRandomLocation } from '../../utilities/vector3';
/**
 * Constants for the Teleport Confusion effect
 */
const TELEPORT_DISTANCE_MIN = 100; // Minimum distance in blocks to teleport the player
const TELEPORT_DISTANCE_EXTRA = 50; // Additional random distance
const Y_OFFSET = 5; // Y offset to avoid teleporting into the ground
/**
 * Event 62: Teleport Confusion - Teleports the player 100 blocks away in a random direction
 *
 * This event:
 * 1. Uses the getRandomLocation utility to find a safe location at least 100 blocks away
 * 2. Teleports the player to the randomly selected location
 * 3. Plays teleport particles and sounds for effect
 *
 * @param player - The player who triggered the event
 */
export function event62(player) {
    try {
        if (!player || !player) {
            return;
        }
        const dimension = player.dimension;
        const playerPosition = player.location;
        // Get a random location at least 100 blocks away from the player
        // The utility will search for a location with air blocks if needed
        const targetPosition = getRandomLocation(playerPosition, dimension, TELEPORT_DISTANCE_MIN, // Min distance of 100 blocks
        TELEPORT_DISTANCE_EXTRA, // Additional random distance
        Y_OFFSET, // Slightly elevated to avoid teleporting into the ground
        true // Check for air blocks to ensure safe teleportation
        );
        // If no safe location was found, log an error and return
        if (!targetPosition) {
            return;
        }
        // Display teleport particle effects before teleporting
        dimension.spawnParticle('minecraft:endrod', playerPosition);
        // Teleport the player
        player.teleport(targetPosition);
        // Display particles at arrival location
        dimension.spawnParticle('minecraft:endrod', targetPosition);
        // Play teleport sound at both locations
        dimension.playSound('mob.endermen.portal', playerPosition);
        dimension.playSound('mob.endermen.portal', targetPosition);
    }
    catch (error) {
    }
}
