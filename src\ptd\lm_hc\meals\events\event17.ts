import { Entity, Player, Vector3, world, system, Dimension, BlockTypes } from '@minecraft/server';
/**
 * Wishing Well data interface for storing well properties
 */
interface WishingWellData {
  center: Vector3;
  radius: number;
  wishesLeft: number;
  createdTime: number;
  // No longer storing originalBlocks - we'll simply deconstruct the well
}

/**
 * Dynamic property name constant for wishing well data
 */
const WISHING_WELL_PROPERTY = 'ptd_lmhc:wishing_well_areas';

/**
 * Event 17: Wishing Well
 * Creates a decorative water well structure near the player with a lower wall for accessibility
 * and a deeper water area. The well can accept up to 3 items as "wishes".
 * Each wish triggers a random good event.
 *
 * @param player The player who triggered the event
 */
export function event17(player: Player): void {
  try {
    const pos = player.location;
    const dim = player.dimension;

    // Define well block types for variety
    const stoneTypes = [
      'minecraft:stone_bricks',
      'minecraft:mossy_stone_bricks',
      'minecraft:cracked_stone_bricks',
      'minecraft:cobblestone',
      'minecraft:mossy_cobblestone'
    ] as const;

    type StoneType = (typeof stoneTypes)[number];
    type SoundType = 'dig.stone' | 'dig.wood' | 'dig.gravel' | 'random.splash' | '';

    // Find a suitable flat area near the player
    const wellCenter: Vector3 = {
      x: Math.floor(pos.x) + 4,
      y: Math.floor(pos.y),
      z: Math.floor(pos.z) + 4
    };

    // Helper function to shuffle an array (Fisher-Yates algorithm)
    function shuffleArray<T>(array: T[]): T[] {
      return [...array].sort(() => Math.random() - 0.5);
    }

    // Helper function to get a random stone type
    function getRandomStoneType(): StoneType {
      const index = Math.floor(Math.random() * stoneTypes.length);
      // This assertion is safe because we're using a literal array with const assertion
      return stoneTypes[index] as StoneType;
    }

    // Helper function to create coordinates for a layer
    function createLayerCoordinates(
      xRange: [number, number],
      zRange: [number, number],
      exclusionPredicate?: (x: number, z: number) => boolean
    ): Array<{ x: number; z: number }> {
      const coordinates: Array<{ x: number; z: number }> = [];

      for (let x = xRange[0]; x <= xRange[1]; x++) {
        for (let z = zRange[0]; z <= zRange[1]; z++) {
          if (!exclusionPredicate || !exclusionPredicate(x, z)) {
            coordinates.push({ x, z });
          }
        }
      }

      return shuffleArray(coordinates);
    }

    // Type definitions for block operations
    type BlockType = (typeof stoneTypes)[number] | 'air' | 'water' | 'oak_fence' | 'oak_slab' | 'oak_planks';

    // Define individual block placements in sequence from bottom to top
    const blockPlacements: Array<{
      x: number;
      y: number;
      z: number;
      blockType: BlockType;
      sound: SoundType;
      volume: number;
      pitch: number;
    }> = [];

    // Well depth from starting point
    const wellDepth = -6;

    // Step 2: Create the base platform foundation - randomized placement within the layer
    const baseCoordinates = createLayerCoordinates([-3, 3], [-3, 3]);
    for (const coord of baseCoordinates) {
      const stoneTypeIndex = Math.floor(Math.random() * stoneTypes.length);
      const stoneType = stoneTypes[stoneTypeIndex] || stoneTypes[0];
      blockPlacements.push({
        x: coord.x,
        y: -1,
        z: coord.z,
        blockType: stoneType,
        sound: 'dig.stone' as const,
        volume: 0.6,
        pitch: 0.8 + Math.random() * 0.4
      });
    }

    // Step 3: Excavate the well shaft and build walls - from top to bedrock
    for (let y = 0; y >= wellDepth; y--) {
      // Clear center 3x3 area
      const excavationCoordinates = createLayerCoordinates([-1, 1], [-1, 1]);
      for (const coord of excavationCoordinates) {
        blockPlacements.push({
          x: coord.x,
          y: y,
          z: coord.z,
          blockType: 'air',
          sound: 'dig.gravel',
          volume: 0.8,
          pitch: 0.7 + Math.random() * 0.6
        });
      }

      // Build stone walls around the shaft at every level
      const wallCoords = createLayerCoordinates(
        [-2, 2],
        [-2, 2],
        (x: number, z: number) => Math.abs(x) <= 1 && Math.abs(z) <= 1
      );
      for (const coord of wallCoords) {
        const stoneType = getRandomStoneType();
        blockPlacements.push({
          x: coord.x,
          y: y,
          z: coord.z,
          blockType: stoneType,
          sound: 'dig.stone',
          volume: 0.6,
          pitch: 0.8 + Math.random() * 0.4
        });
      }
    }

    // Step 4: Place the stone bottom of the well - randomized placement
    // Create bottom layer
    const bottomCoordinates = createLayerCoordinates([-1, 1], [-1, 1]);
    for (const coord of bottomCoordinates) {
      const stoneType = getRandomStoneType();
      blockPlacements.push({
        x: coord.x,
        y: wellDepth,
        z: coord.z,
        blockType: stoneType,
        sound: 'dig.stone',
        volume: 0.6,
        pitch: 0.8 + Math.random() * 0.4
      });
    }

    // Step 5: Fill with water - randomized placement from bottom to top
    let isFirstWater = true;
    // Fill with water from bottom to -1
    for (let y = wellDepth + 1; y <= -1; y++) {
      const waterCoordinates = createLayerCoordinates([-1, 1], [-1, 1]);
      for (const coord of waterCoordinates) {
        blockPlacements.push({
          x: coord.x,
          y: y,
          z: coord.z,
          blockType: 'water',
          sound: isFirstWater ? 'random.splash' : '',
          volume: 1.0,
          pitch: 1.0
        });
        isFirstWater = false;
      }
    }

    // Step 5: Build the ground level outer walls - randomized placement
    const wallsExclusionPredicate = (x: number, z: number): boolean => Math.abs(x) <= 1 && Math.abs(z) <= 1;
    const wallsCoordinates = createLayerCoordinates([-2, 2], [-2, 2], wallsExclusionPredicate);

    for (const coord of wallsCoordinates) {
      const stoneType = getRandomStoneType() satisfies BlockType;
      blockPlacements.push({
        x: coord.x,
        y: 0,
        z: coord.z,
        blockType: stoneType,
        sound: 'dig.stone',
        volume: 0.8,
        pitch: 0.7 + Math.random() * 0.6
      });
    }

    // Step 6 & 7: Add the fence posts (bottom and top level) - randomized placement
    const corners: Array<{ x: number; z: number }> = shuffleArray([
      { x: -2, z: -2 },
      { x: -2, z: 2 },
      { x: 2, z: -2 },
      { x: 2, z: 2 }
    ]);

    // Bottom level fences
    for (const corner of corners) {
      blockPlacements.push({
        x: corner.x,
        y: 1,
        z: corner.z,
        blockType: 'oak_fence',
        sound: 'dig.wood',
        volume: 1.0,
        pitch: 0.9 + Math.random() * 0.2
      });
    }

    // Top level fences (reshuffled for different order)
    const reshuffledCorners = shuffleArray([...corners]);
    for (const corner of reshuffledCorners) {
      blockPlacements.push({
        x: corner.x,
        y: 2,
        z: corner.z,
        blockType: 'oak_fence',
        sound: 'dig.wood',
        volume: 1.0,
        pitch: 0.9 + Math.random() * 0.2
      });
    }

    // Step 8: Add roof slabs - randomized placement (except center)
    const roofCoordinates = createLayerCoordinates(
      [-2, 2],
      [-2, 2],
      (x: number, z: number) => x === 0 && z === 0
    );
    for (const coord of roofCoordinates) {
      blockPlacements.push({
        x: coord.x,
        y: 3,
        z: coord.z,
        blockType: 'oak_slab',
        sound: 'dig.wood',
        volume: 0.8,
        pitch: 0.7 + Math.random() * 0.4
      });
    }

    // Step 9: Replace center roof block with solid planks - always last in this layer
    blockPlacements.push({
      x: 0,
      y: 3,
      z: 0,
      blockType: 'oak_planks',
      sound: 'dig.wood',
      volume: 1.0,
      pitch: 0.8
    });

    // Execute block placements one at a time with appropriate timing
    let currentBlock = 0;
    const blockInterval = system.runInterval(() => {
      try {
        if (currentBlock < blockPlacements.length) {
          const placement = blockPlacements[currentBlock];
          if (!placement) {
            currentBlock++;
            return true;
          }

          // Calculate world position for block placement
          const worldPos = {
            x: Math.floor(wellCenter.x + placement.x),
            y: Math.floor(wellCenter.y + placement.y),
            z: Math.floor(wellCenter.z + placement.z)
          };

          // Get the current block at the position
          const targetBlock = dim.getBlock(worldPos);

          // Place the block using the native API
          if (targetBlock) {
            // Convert the string blockType to BlockType using BlockTypes.get()
            const blockType = BlockTypes.get(placement.blockType);
            if (blockType) {
              targetBlock.setType(blockType);
            } else {
            }

            // Play sound if specified and not empty
            if (placement.sound && placement.sound.length > 0) {
              dim.playSound(placement.sound, worldPos, {
                volume: placement.volume,
                pitch: placement.pitch
              });
            }
          } else {
          }

          currentBlock++;
          return true;
        }

        // All blocks placed, now finish with particles and registration
        for (let i = 0; i < 10; i++) {
          const particleX = wellCenter.x + (Math.random() * 2 - 1);
          const particleZ = wellCenter.z + (Math.random() * 2 - 1);
          player.dimension.spawnParticle('minecraft:water_splash', {
            x: particleX,
            y: wellCenter.y - 0.5,
            z: particleZ
          });
        }

        // Play completion sound
        dim.playSound('bucket.fill_water', wellCenter, {
          volume: 1.0,
          pitch: 1.0
        });

        // Register the well (no longer storing original blocks)
        createWishingWellArea(wellCenter, player);

        system.clearRun(blockInterval);
        return false;
      } catch (error) {
        system.clearRun(blockInterval);
        return false;
      }
    }, 1); // 1 tick delay between each block placement for faster construction
  } catch (error) {
  }
}

/**
 * Converts a Vector3 to a string key for storing in maps
 * @param location The Vector3 to convert
 * @returns A string representation of the location
 */
export function locationToString(location: Vector3): string {
  return `${Math.floor(location.x)}_${Math.floor(location.y)}_${Math.floor(location.z)}`;
}

/**
 * Adds a wishing well area to the world's dynamic properties
 * @param wellCenter The center coordinates of the well
 * @param player The player who created the well
 */
export function createWishingWellArea(wellCenter: Vector3, player: Player): void {
  try {
    const wellTop: Vector3 = {
      x: wellCenter.x,
      y: wellCenter.y + 1,
      z: wellCenter.z
    };

    const wellData: WishingWellData = {
      center: wellTop,
      radius: 3.0, // Increased from 1.5 to 3.0 for better accessibility
      wishesLeft: 3,
      createdTime: Date.now()
    };

    let existingWells: Record<string, WishingWellData> = {};
    if (world.getDynamicProperty(WISHING_WELL_PROPERTY)) {
      try {
        existingWells = JSON.parse(world.getDynamicProperty(WISHING_WELL_PROPERTY) as string);
      } catch (error) {
        existingWells = {};
      }
    }

    const wellKey = locationToString(wellTop);
    existingWells[wellKey] = wellData;

    world.setDynamicProperty(WISHING_WELL_PROPERTY, JSON.stringify(existingWells));

    // Debug messages

    player.sendMessage("§6✧ You've created a §bWishing Well§6! Drop items into it to make wishes... ✧");
  } catch (error) {
  }
}

/**
 * Processes an item dropped into a wishing well
 * @param wellLocation The location key of the well
 * @returns Whether the wish was processed successfully
 */
export function processWishingWellItemDrop(wellLocation: string, player: Player): boolean {
  try {
    // Get existing wells
    let wells: Record<string, WishingWellData> = {};
    if (world.getDynamicProperty(WISHING_WELL_PROPERTY)) {
      wells = JSON.parse(world.getDynamicProperty(WISHING_WELL_PROPERTY) as string);
    } else {
      return false; // No wells exist
    }

    // Check if this well exists
    if (!wells[wellLocation]) {
      return false;
    }

    // Store well data before modifying
    const wellData = wells[wellLocation];
    const wellPos = wellData.center;

    // Process wish
    wellData.wishesLeft--;

    // Trigger a random good event (odd numbers 1-75)
    try {
      // Generate a random odd number between 1 and 75
      // Start with a random number 0-37, multiply by 2, and add 1 to make it odd
      const randomEventNumber = Math.floor(Math.random() * 38) * 2 + 1;

      // Make sure it's within our range (1-75)
      const eventNumber = Math.min(randomEventNumber, 75);

      // Trigger the event
      player.sendMessage(`§6✧ The §bWishing Well§6 grants your wish! §e(Event ${eventNumber}) §6✧`);

      // Import and trigger the corresponding event dynamically
      import(`./event${eventNumber}`)
        .then((eventModule) => {
          if (typeof eventModule[`event${eventNumber}`] === 'function') {
            eventModule[`event${eventNumber}`](player);
          } else {
          }
        })
        .catch((error) => {
        });
    } catch (error) {
    }

    // Send message about remaining wishes
    if (wellData.wishesLeft > 0) {
      player.sendMessage(
        `§6✧ The well has ${wellData.wishesLeft} ${
          wellData.wishesLeft === 1 ? 'wish' : 'wishes'
        } remaining! ✧`
      );
    } else {
      // This was the last wish
      player.sendMessage(
        `§6✧ The §bWishing Well§6 has granted all its wishes! It glows with magical energy! ✧`
      );

      // Add some magical effects for the last wish
      for (let i = 0; i < 20; i++) {
        const offsetX = (Math.random() - 0.5) * 3;
        const offsetY = Math.random() * 2;
        const offsetZ = (Math.random() - 0.5) * 3;

        player.dimension.spawnParticle('minecraft:heart_particle', {
          x: wellPos.x + offsetX,
          y: wellPos.y + offsetY,
          z: wellPos.z + offsetZ
        });
      }

      player.dimension.runCommand(
        `playsound random.levelup @a ${wellPos.x} ${wellPos.y} ${wellPos.z} 1.0 1.5`
      );

      // Clear the well data since all wishes are exhausted
      clearWishingWellData(wellLocation, player.dimension);
      return true;
    }

    // Update well data in the collection
    wells[wellLocation] = wellData;

    // Save updated well collection
    world.setDynamicProperty(WISHING_WELL_PROPERTY, JSON.stringify(wells));
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Checks if an item was dropped into a wishing well and processes it
 * @param entity The entity to check (expected to be an item)
 * @returns Whether the item was processed by a wishing well
 */
export function handleItemDropInWell(entity: Entity): boolean {
  try {
    if (entity.typeId !== 'minecraft:item') return false;

    const itemPos = entity.location;
    const wellProperty = world.getDynamicProperty(WISHING_WELL_PROPERTY);

    if (!wellProperty) {
      return false;
    }

    try {
      const wells: Record<string, WishingWellData> = JSON.parse(wellProperty as string);

      // Get the nearest player within 2 blocks of the dropped item
      const [nearestPlayer] = entity.dimension.getPlayers({ closest: 1, maxDistance: 2, location: itemPos });

      // Early return if no player is found to save resources
      if (!nearestPlayer) {
        return false;
      }

      for (const wellKey in wells) {
        const well = wells[wellKey];
        if (!well?.center) continue;

        const wellCenter = well.center;
        const distance = Math.sqrt(
          Math.pow(wellCenter.x - itemPos.x, 2) +
            Math.pow(wellCenter.y - itemPos.y, 2) +
            Math.pow(wellCenter.z - itemPos.z, 2)
        );


        if (distance <= well.radius) {
          processWishingWellItemDrop(wellKey, nearestPlayer);

          entity.dimension.spawnParticle('minecraft:water_splash', itemPos);
          entity.dimension.spawnParticle('minecraft:heart_particle', itemPos);

          // Use playSound instead of runCommand
          entity.dimension.playSound('random.orb', itemPos, {
            volume: 1.0,
            pitch: 1.0
          });

          // Remove the item entity
          entity.kill();

          return true;
        }
      }
    } catch (error) {
    }

    return false;
  } catch (error) {
    return false;
  }
}

/**
 * Clears the wishing well data and initiates well deconstruction.
 * The well data is removed from storage immediately before starting the deconstruction animation
 * to prevent multiple deconstruction instances from being created if items are dropped during
 * the deconstruction process.
 *
 * @param wellLocation The string key identifying the well location
 * @param dimension The dimension where the well exists
 */
export function clearWishingWellData(wellLocation: string, dimension: Dimension): void {
  try {
    const wellProperty = world.getDynamicProperty(WISHING_WELL_PROPERTY);
    if (!wellProperty) {
      return;
    }

    const wells: Record<string, WishingWellData> = JSON.parse(wellProperty as string);

    // Check if this well exists
    if (!(wellLocation in wells)) {
      return;
    }

    const wellData = wells[wellLocation];
    if (!wellData) {
      return;
    }

    // IMPORTANT: Clear the well data from storage immediately to prevent multiple deconstruction instances
    const storedWellData = { ...wellData }; // Keep a copy for the deconstruction process
    delete wells[wellLocation];

    // Update persistent storage immediately
    if (Object.keys(wells).length > 0) {
      world.setDynamicProperty(WISHING_WELL_PROPERTY, JSON.stringify(wells));
    } else {
      world.setDynamicProperty(WISHING_WELL_PROPERTY, undefined);
    }

    // Create block placements for deconstruction using our stored copy
    const wellCenter = {
      x: storedWellData.center.x,
      y: storedWellData.center.y - 1, // Adjust to actual well center
      z: storedWellData.center.z
    };

    // Play a sound effect to indicate deconstruction is beginning
    try {
      dimension.playSound('random.totem', storedWellData.center, { volume: 0.4 });
    } catch (error) {
      // Ignore sound errors as they're not critical
    }

    // Type for well block IDs
    type WellBlockId = string;

    // Define well block types for deconstruction
    const stoneTypes = [
      'minecraft:stone_bricks',
      'minecraft:mossy_stone_bricks',
      'minecraft:cracked_stone_bricks',
      'minecraft:cobblestone',
      'minecraft:mossy_cobblestone'
    ] as const;

    // Helper function to determine block type based on position
    function getBlockTypeAtPosition(x: number, y: number, z: number): WellBlockId {
      const randomStoneType = () => {
        return stoneTypes[Math.floor(Math.random() * stoneTypes.length)] ?? stoneTypes[0];
      };

      if (y === 3) {
        return x === 0 && z === 0 ? 'minecraft:oak_planks' : 'minecraft:oak_slab';
      }

      if (y === 2 || y === 1) {
        if ((x === -2 || x === 2) && (z === -2 || z === 2)) {
          return 'minecraft:oak_fence';
        }
        return Math.abs(x) <= 1 && Math.abs(z) <= 1 ? 'minecraft:air' : randomStoneType();
      }

      if (y === 0) {
        return Math.abs(x) <= 1 && Math.abs(z) <= 1 ? 'minecraft:water' : randomStoneType();
      }

      if (y === -1 && Math.abs(x) <= 3 && Math.abs(z) <= 3) {
        return randomStoneType();
      }

      if (y < 0 && y > -6) {
        if (Math.abs(x) <= 1 && Math.abs(z) <= 1) {
          return 'minecraft:water';
        }
        if (Math.abs(x) <= 2 && Math.abs(z) <= 2) {
          return randomStoneType();
        }
      }

      if (y === -6) {
        return randomStoneType();
      }

      return 'minecraft:air';
    }

    // Helper function to determine sound based on block type
    function getSoundForBlockType(blockType: string): { sound: string; volume: number; pitch: number } {
      if (blockType.includes('stone') || blockType.includes('cobble')) {
        return {
          sound: 'dig.stone',
          volume: 0.6,
          pitch: 0.8 + Math.random() * 0.4
        };
      } else if (
        blockType.includes('oak') ||
        blockType.includes('plank') ||
        blockType.includes('fence') ||
        blockType.includes('slab')
      ) {
        return {
          sound: 'dig.wood',
          volume: 0.8,
          pitch: 0.7 + Math.random() * 0.4
        };
      } else if (blockType.includes('water')) {
        return {
          sound: 'random.splash',
          volume: 1.0,
          pitch: 1.0
        };
      } else {
        return {
          sound: 'dig.gravel',
          volume: 0.5,
          pitch: 0.9 + Math.random() * 0.2
        };
      }
    }

    // Generate block positions for deconstruction
    // We'll start from the top (y=3) and work our way down to the bottom (y=-6)
    const blockPositions: Array<{ x: number; y: number; z: number; blockType: string }> = [];

    // This loop collects all the positions we need to remove, starting from the top
    for (let y = 3; y >= -6; y--) {
      // Different handling for each layer
      if (y === 3) {
        // Roof layer (oak planks and slabs)
        for (let x = -2; x <= 2; x++) {
          for (let z = -2; z <= 2; z++) {
            blockPositions.push({
              x,
              y,
              z,
              blockType: getBlockTypeAtPosition(x, y, z)
            });
          }
        }
      } else if (y === 2 || y === 1) {
        // Fence layers
        // Only add corner positions for fences
        const corners = [
          { x: -2, z: -2 },
          { x: -2, z: 2 },
          { x: 2, z: -2 },
          { x: 2, z: 2 }
        ];

        for (const corner of corners) {
          blockPositions.push({
            x: corner.x,
            y,
            z: corner.z,
            blockType: 'minecraft:oak_fence'
          });
        }
      } else if (y === -1) {
        // Add the base platform layer
        for (let x = -3; x <= 3; x++) {
          for (let z = -3; z <= 3; z++) {
            const blockType = getBlockTypeAtPosition(x, y, z);
            if (blockType !== 'minecraft:air') {
              blockPositions.push({ x, y, z, blockType });
            }
          }
        }
      } else if (y >= -6 && y <= 0) {
        // Well shaft and surrounding walls
        for (let x = -2; x <= 2; x++) {
          for (let z = -2; z <= 2; z++) {
            // Skip air blocks in the center shaft (optimization)
            if (!(Math.abs(x) <= 1 && Math.abs(z) <= 1 && y < 0 && y > -6)) {
              const blockType = getBlockTypeAtPosition(x, y, z);
              if (blockType !== 'minecraft:air') {
                blockPositions.push({ x, y, z, blockType });
              }
            }
          }
        }
      }
    }

    // Type for block operations
    type WellBlockType =
      | 'minecraft:stone_bricks'
      | 'minecraft:mossy_stone_bricks'
      | 'minecraft:cracked_stone_bricks'
      | 'minecraft:cobblestone'
      | 'minecraft:mossy_cobblestone'
      | 'minecraft:oak_fence'
      | 'minecraft:oak_slab'
      | 'minecraft:oak_planks'
      | 'minecraft:air'
      | 'minecraft:water';

    // Randomize the blocks within each layer for a more natural deconstruction
    const blocksByLayer: Record<
      number,
      Array<{ x: number; y: number; z: number; blockType: WellBlockType }>
    > = {};

    // Group blocks by y-coordinate (layer)
    for (const block of blockPositions) {
      const y = block.y;
      if (!blocksByLayer[y]) {
        blocksByLayer[y] = [];
      }
      blocksByLayer[y]!.push({
        ...block,
        blockType: block.blockType as WellBlockType
      });
    }

    // Shuffle each layer
    Object.entries(blocksByLayer).forEach(([y, blocks]) => {
      if (blocks) {
        blocksByLayer[Number(y)] = blocks.sort(() => Math.random() - 0.5);
      }
    });

    // Set timeout to give particles time to display before deconstruction begins
    system.runTimeout(() => {
      try {
        // Start with layer 3 (top) and work down
        const layers = Object.keys(blocksByLayer)
          .map(Number)
          .sort((a, b) => a - b)
          .reverse();

        // Track current progress
        let currentLayerIndex = 0;
        let currentBlockInLayer = 0;

        // One block per tick deconstruction interval
        const deconstructInterval = system.runInterval(() => {
          try {
            if (currentLayerIndex >= layers.length) {
              // All layers processed, clean up and exit
              system.clearRun(deconstructInterval);

              // Play completion sound
              try {
                if (storedWellData?.center) {
                  dimension.playSound('random.levelup', storedWellData.center, {
                    volume: 1.0,
                    pitch: 0.7
                  });

                  // Spawn final effect particles
                  for (let i = 0; i < 15; i++) {
                    const offsetX = (Math.random() - 0.5) * 4;
                    const offsetY = Math.random() * 3;
                    const offsetZ = (Math.random() - 0.5) * 4;

                    dimension.spawnParticle('minecraft:totem_particle', {
                      x: storedWellData.center.x + offsetX,
                      y: storedWellData.center.y + offsetY,
                      z: storedWellData.center.z + offsetZ
                    });
                  }
                }
              } catch (error) {
                // Ignore sound errors
              }

              return false;
            }

            const currentLayer = layers[currentLayerIndex] ?? -1;
            const layerBlocks = blocksByLayer[currentLayer] ?? [];

            if (!Array.isArray(layerBlocks) || currentBlockInLayer >= layerBlocks.length) {
              // Move to next layer if current layer is invalid or depleted
              currentLayerIndex++;
              currentBlockInLayer = 0;
              return true;
            }

            // Get next block to remove
            const block = layerBlocks[currentBlockInLayer] ?? null;
            if (!block) {
              currentBlockInLayer++;
              return true;
            }
            currentBlockInLayer++;

            // Calculate world position
            const worldPos = {
              x: Math.floor(wellCenter.x + block.x),
              y: Math.floor(wellCenter.y + block.y),
              z: Math.floor(wellCenter.z + block.z)
            };

            // Get the current block
            const targetBlock = dimension.getBlock(worldPos);

            if (targetBlock) {
              // Get appropriate sound for this block type
              const soundInfo = getSoundForBlockType(block.blockType);

              // Play sound for removal
              dimension.playSound(soundInfo.sound, worldPos, {
                volume: soundInfo.volume,
                pitch: soundInfo.pitch
              });

              // Remove block (replace with air)
              targetBlock.setType('minecraft:air');

              // Add deconstruction particles
              if (Math.random() < 0.7) {
                const particleType = 'minecraft:cauldron_explosion_emitter';
                dimension.spawnParticle(particleType, {
                  x: worldPos.x + (Math.random() - 0.5) * 0.5,
                  y: worldPos.y + 0.3,
                  z: worldPos.z + (Math.random() - 0.5) * 0.5
                });
              }
            }

            return true;
          } catch (intervalError) {
            system.clearRun(deconstructInterval);

            // Just log the error and return since well data is already cleared
            return false;
          }
        }, 1); // Run every tick for one-block-per-tick deconstruction
      } catch (error) {
      }
    }, 10); // Wait half a second before starting deconstruction
  } catch (error) {
  }
}
