import { Player, ItemStack, EntityInventoryComponent } from '@minecraft/server';

/**
 * Lucky Elixir: A special potion that grants invisibility, speed, and absorption effects
 * for 5 minutes (300 seconds = 6000 ticks) when consumed.
 * Appears identical to the Unlucky Elixir to create mystery for the player.
 * @param player The player who consumed the Lucky Elixir
 */
export function handleLuckyElixirConsume(player: Player): void {
  try {
    // Convert 5 minutes to ticks (5 minutes * 60 seconds * 20 ticks = 6000 ticks)
    const durationInTicks = 6000;

    // Apply Invisibility effect for 5 minutes (6000 ticks)
    player.addEffect('invisibility', durationInTicks, {
      amplifier: 0,
      showParticles: false
    });

    // Apply Speed effect for 5 minutes (6000 ticks)
    player.addEffect('speed', durationInTicks, {
      amplifier: 1, // Speed II for better mobility
      showParticles: true
    });

    // Apply Absorption effect for 5 minutes (6000 ticks)
    player.addEffect('absorption', durationInTicks, {
      amplifier: 3, // Absorption IV for significant health boost
      showParticles: true
    });
  } catch (error) {
  }
  return;
}

/**
 * Event 23: Lucky Elixir
 * Creates a mysterious potion that grants positive effects when consumed.
 * The potion appears identical to the Unlucky Elixir (event 44) to create
 * an element of surprise and risk for the player.
 * @param player The player who triggered the event
 */
export function event23(player: Player): void {
  try {
    // Create a Lucky Elixir item stack
    const luckyElixir = new ItemStack('ptd_lmhc:lucky_elixir', 1);

    // Set custom name with formatting
    luckyElixir.nameTag = '§b§lLucky Elixir';

    // Add mysterious lore that doesn't reveal whether it's good or bad
    luckyElixir.setLore([
      '§dA mysterious potion with unpredictable effects',
      '§7The liquid swirls with strange energy',
      '§7Only the brave would dare to drink it'
    ]);

    // Set a dynamic property to identify this as a lucky elixir
    // This will be used to determine which effects to apply when consumed
    luckyElixir.setDynamicProperty('ptd_lmhc:is_unlucky_elixir', false);

    // Give the Lucky Elixir to the player
    const inventory = player.getComponent('inventory') as EntityInventoryComponent;
    if (inventory) {
      const container = inventory.container;
      if (container) {
        container.addItem(luckyElixir);
      }
    }

    // Play sound to indicate item received
    player.playSound('random.levelup', { volume: 0.5, pitch: 1.0 });

    // Spawn particles for visual effect
    for (let i = 0; i < 10; i++) {
      player.dimension.spawnParticle('minecraft:endrod', {
        x: player.location.x,
        y: player.location.y + 1,
        z: player.location.z
      });
    }
  } catch (error) {
  }
  return;
}
