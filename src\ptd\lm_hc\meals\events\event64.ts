import { Player, Vector3 } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';
import { EntityQuantityConfig, spawnEntitiesWithInterval } from '../../utilities/summonEntity';

/**
 * Event 64: Johnny's Here - Spawns 3 Vindicators named <PERSON> that will attack everything
 * Using the Easter egg in Minecraft where Vindicators named <PERSON> will attack all entities
 *
 * @param player - The player who triggered the event
 */
export function event64(player: Player): void {
  try {
    // Configure vindicator entity type and quantity
    const entityConfigs: EntityQuantityConfig[] = [{ entityId: 'minecraft:vindicator', count: 3 }];

    /**
     * Gets a random spawn location for vindicators, ensuring there's air
     * @returns Valid spawn Vector3 position or undefined if none found
     */
    const getSpawnLocation = (): Vector3 | undefined => {
      // Get a random location using the utility function
      return getRandomLocation(
        player.location,
        player.dimension,
        5, // Base offset (minimum distance from player)
        8, // Additional random offset
        1, // Y offset (slightly above ground)
        true // Check for air blocks
      );
    };

    // Start spawning vindicators with a 4 tick delay between each
    spawnEntitiesWithInterval(
      player.dimension,
      entityConfigs,
      getSpawnLocation,
      4, // Delay between spawns in ticks
      (vindicator) => {
        // Name the vindicator "Johnny" to make them attack everything
        if (vindicator && vindicator) {
          // Set the vindicator's name tag
          vindicator.nameTag = 'Johnny';

          // Play the cauldron explosion particle at the vindicator's location
          player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', vindicator.location);

          // Play enderman teleport sound for effect
          player.dimension.playSound('mob.endermen.portal', vindicator.location);
        }
      }
    ).catch((error) => {
    });
  } catch (error) {
  }
  return;
}
