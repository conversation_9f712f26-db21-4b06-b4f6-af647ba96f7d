/**
 * Module for handling Lucky Pot entity interactions and behaviors.
 * The Lucky Pot is a special container that creates magical meals from regular food items.
 */

import {
  Entity,
  Player,
  world,
  ItemStack,
  Vector3,
  EntityEquippableComponent,
  EquipmentSlot,
  EntityComponentTypes
} from '@minecraft/server';
import { getDistance } from '../utilities/vector3';
import { decrementStackMainHand } from '../utilities/items/decrementStack';
import { hasAchievement, MAX_ACHIEVEMENTS } from './achievement';

/**
 * Set of valid food items that can be used in the Lucky Pot.
 * Includes all vanilla food items except golden variants for balance purposes.
 * Also includes raw food items and crops for additional recipe options.
 * @constant {Set<string>}
 */
const validFoodItems = new Set<string>([
  'minecraft:apple',
  'minecraft:baked_potato',
  'minecraft:bamboo',
  'minecraft:beef',
  'minecraft:beetroot',
  'minecraft:beetroot_seeds',
  'minecraft:beetroot_soup',
  'minecraft:bread',
  'minecraft:bone',
  'minecraft:bone_meal',
  'minecraft:cake',
  'minecraft:carrot',
  'minecraft:chicken',
  'minecraft:chorus_fruit',
  'minecraft:cocoa_beans',
  'minecraft:cod',
  'minecraft:cooked_beef',
  'minecraft:cooked_chicken',
  'minecraft:cooked_cod',
  'minecraft:cooked_mutton',
  'minecraft:cooked_porkchop',
  'minecraft:cooked_rabbit',
  'minecraft:cooked_salmon',
  'minecraft:cookie',
  'minecraft:dried_kelp',
  'minecraft:glow_berries',
  'minecraft:honey_bottle',
  'minecraft:kelp',
  'minecraft:melon_seeds',
  'minecraft:melon_slice',
  'minecraft:mushroom_stew',
  'minecraft:mutton',
  'minecraft:poisonous_potato',
  'minecraft:porkchop',
  'minecraft:potato',
  'minecraft:pufferfish',
  'minecraft:pumpkin',
  'minecraft:pumpkin_pie',
  'minecraft:pumpkin_seeds',
  'minecraft:rabbit',
  'minecraft:rabbit_stew',
  'minecraft:rotten_flesh',
  'minecraft:salmon',
  'minecraft:spider_eye',
  'minecraft:sugar',
  'minecraft:sugar_cane',
  'minecraft:suspicious_stew',
  'minecraft:sweet_berries',
  'minecraft:torchflower_seeds',
  'minecraft:tropical_fish',
  'minecraft:wheat',
  'minecraft:wheat_seeds',
  'minecraft:egg',
  'minecraft:brown_egg',
  'minecraft:blue_egg',
  'minecraft:brown_mushroom',
  'minecraft:red_mushroom',
]);

/**
 * Set of expensive golden food items that are explicitly forbidden in the Lucky Pot.
 * These items are too valuable to be used as ingredients.
 * @constant {Set<string>}
 */
const expensiveGoldenFoods = new Set<string>([
  'minecraft:golden_apple',
  'minecraft:enchanted_golden_apple',
  'minecraft:glistering_melon_slice',
  'minecraft:golden_carrot'
]);

/**
 * Set of ingredients required to craft the Lucky Food Trophy.
 * @constant {Set<string>}
 */
const trophyIngredients = new Set<string>([
  'minecraft:golden_helmet',
  'minecraft:golden_chestplate',
  'minecraft:golden_leggings',
  'minecraft:golden_boots',
  'minecraft:golden_shovel'
]);

/**
 * Set of all available lucky meals that can be created by the Lucky Pot.
 * Each meal has unique effects and properties.
 * @constant {Set<string>}
 */
const luckyMeals = new Set<string>([
  'ptd_lmhc:abalone',
  'ptd_lmhc:banana_waffle',
  'ptd_lmhc:bananadog',
  'ptd_lmhc:beef_stew_rice',
  'ptd_lmhc:bibimbap',
  'ptd_lmhc:blueberry_waffle',
  'ptd_lmhc:bonbon_bowl',
  'ptd_lmhc:brain_stew',
  'ptd_lmhc:cheese_shell_soup',
  'ptd_lmhc:cookie_waffle',
  'ptd_lmhc:cookiekiwi_waffle',
  'ptd_lmhc:cream_cheese_waffle',
  'ptd_lmhc:crusty_cheese_cup',
  'ptd_lmhc:curryrice',
  'ptd_lmhc:dumpling_soup',
  'ptd_lmhc:eggbacon',
  'ptd_lmhc:fish_dish',
  'ptd_lmhc:fish_taco',
  'ptd_lmhc:flavorboom_waffle',
  'ptd_lmhc:freshcream_waffle',
  'ptd_lmhc:fried_chicken',
  'ptd_lmhc:fried_squid',
  'ptd_lmhc:fruitcream_sandwiches',
  'ptd_lmhc:fruits_waffle',
  'ptd_lmhc:fudge_pop',
  'ptd_lmhc:delicious_rice',
  'ptd_lmhc:grape_waffle',
  'ptd_lmhc:iatethis',
  'ptd_lmhc:ice_cream_waffle',
  'ptd_lmhc:jar_of_pickles',
  'ptd_lmhc:jelly_cake',
  'ptd_lmhc:jjajangmyeon',
  'ptd_lmhc:kimbap_sushi',
  'ptd_lmhc:limesicle_pop',
  'ptd_lmhc:meal_worms',
  'ptd_lmhc:octopus_sandwich',
  'ptd_lmhc:omurice',
  'ptd_lmhc:onigiri',
  'ptd_lmhc:orange_chicken',
  'ptd_lmhc:orange_drizzled_ham',
  'ptd_lmhc:pancakes',
  'ptd_lmhc:pizza',
  'ptd_lmhc:prawnburger_waffle',
  'ptd_lmhc:ramen_galore',
  'ptd_lmhc:raspberry_cream_cake',
  'ptd_lmhc:rotten_food',
  'ptd_lmhc:sashimi',
  'ptd_lmhc:shrimp_partae',
  'ptd_lmhc:shrimpfry',
  'ptd_lmhc:silky_salmon',
  'ptd_lmhc:skewered_selections',
  'ptd_lmhc:soup_of_slop',
  'ptd_lmhc:spaghetti',
  'ptd_lmhc:split_pea_soup',
  'ptd_lmhc:steak',
  'ptd_lmhc:steamed_claw',
  'ptd_lmhc:steamed_variety',
  'ptd_lmhc:strawberry_cream_cake',
  'ptd_lmhc:strawberry_waffle',
  'ptd_lmhc:striped_sandwich',
  'ptd_lmhc:sushi_template',
  'ptd_lmhc:sweet_waffle',
  'ptd_lmhc:talkin_turkey',
  'ptd_lmhc:teriyaki',
  'ptd_lmhc:toasted_cheese_muffin',
  'ptd_lmhc:toppoki',
  'ptd_lmhc:triple_nougat_cream',
  'ptd_lmhc:tripleleche_cake',
  'ptd_lmhc:tropical_delight',
  'ptd_lmhc:truloves_first_bite',
  'ptd_lmhc:turkish_delights',
  'ptd_lmhc:vegetable_waffle',
  'ptd_lmhc:waffle',
  'ptd_lmhc:waffle_cake',
  'ptd_lmhc:wrapped_spinach_chicken'
]);

/**
 * Dynamic property key for tracking used ingredients
 * @constant {string}
 */
const USED_INGREDIENTS_PROPERTY = 'ptd_lmhc:used_ingredients';

/**
 * Dynamic property key for storing ingredients for return on destruction
 * @constant {string}
 */
const STORED_INGREDIENTS_PROPERTY = 'ptd_lmhc:stored_ingredients';

/**
 * Dynamic property key for storing trophy ingredients
 * @constant {string}
 */
const TROPHY_INGREDIENTS_PROPERTY = 'ptd_lmhc:trophy_ingredients';

/**
 * Interface for stored ingredient data
 * @interface StoredIngredient
 */
interface StoredIngredient {
  typeId: string;
  amount: number;
}

/**
 * Checks if the player has unlocked all achievements required for the trophy
 * @param player - The player to check achievements for
 * @returns {boolean} True if all achievements are unlocked, false otherwise
 */
function hasUnlockedAllAchievements(player: Player): boolean {
  // Check each achievement from 1 to MAX_ACHIEVEMENTS
  for (let i = 1; i <= MAX_ACHIEVEMENTS; i++) {
    if (!hasAchievement(player, i)) {
      return false; // Return false as soon as one achievement is missing
    }
  }

  // If we get here, all achievements are unlocked
  return true;
}

/**
 * Checks if the given items match the trophy ingredients exactly
 * @param ingredients - Array of item type IDs to check
 * @returns {boolean} True if ingredients match trophy requirements
 */
function isTrophyRecipe(ingredients: string[]): boolean {
  // Must have exactly 5 ingredients
  if (ingredients.length !== 5) {
    return false;
  }

  // Check if all trophy ingredients are included
  for (const ingredient of trophyIngredients) {
    if (!ingredients.includes(ingredient)) {
      return false;
    }
  }

  // Ensure no extra ingredients beyond the trophy set
  for (const ingredient of ingredients) {
    if (!trophyIngredients.has(ingredient)) {
      return false;
    }
  }

  return true;
}

/**
 * Returns stored trophy ingredients to the player
 * @param entity - The Lucky Pot entity
 * @param player - The player to return ingredients to
 */
function returnTrophyIngredientsToPlayer(entity: Entity, player: Player): void {
  const trophyIngredientsStr = entity.getDynamicProperty(TROPHY_INGREDIENTS_PROPERTY) as string;

  if (trophyIngredientsStr) {
    const trophyIngredients: StoredIngredient[] = JSON.parse(trophyIngredientsStr);
    const dimension = entity.dimension;
    const spawnPos = entity.location;

    // Calculate direction for spawned items to go toward player
    const directionToPlayer: Vector3 = {
      x: player.location.x - spawnPos.x,
      y: player.location.y - spawnPos.y,
      z: player.location.z - spawnPos.z
    };

    // Normalize the direction vector based on distance
    const distance = getDistance(spawnPos, player.location);
    const normalizedDirection: Vector3 = {
      x: (directionToPlayer.x / distance) * 0.1,
      y: 0.15,
      z: (directionToPlayer.z / distance) * 0.1
    };

    // Return each trophy ingredient
    for (const ingredient of trophyIngredients) {
      const itemStack = new ItemStack(ingredient.typeId, ingredient.amount);
      dimension.spawnItem(itemStack, spawnPos).applyImpulse(normalizedDirection);
    }

    // Reset trophy ingredients
    entity.setDynamicProperty(TROPHY_INGREDIENTS_PROPERTY, '[]');

    // Reset the pot count
    entity.setProperty('ptd_lmhc:ingredient_count', 0);

    // Reset used ingredients
    entity.setDynamicProperty(USED_INGREDIENTS_PROPERTY, '[]');

    // Play return sound
    dimension.playSound('pot.error', spawnPos, { volume: 1 });
  }
}

/**
 * Resets all properties of the Lucky Pot to their initial state
 * @param pot - The Lucky Pot entity to reset
 */
function resetPotProperties(pot: Entity): void {
  try {
    pot.setDynamicProperty(USED_INGREDIENTS_PROPERTY, '[]');
    pot.setDynamicProperty(STORED_INGREDIENTS_PROPERTY, '[]');
    pot.setDynamicProperty(TROPHY_INGREDIENTS_PROPERTY, '[]');
    pot.setProperty('ptd_lmhc:ingredient_count', 0);
  } catch (error) {
  }
}

/**
 * Handles player interaction with the Lucky Pot entity.
 * This function manages the ingredient addition process and food creation mechanics.
 *
 * @param LuckyPot - The Lucky entity being interacted with
 * @param player - The player performing the interaction
 * @param item - The item stack in the player's hand (if any)
 *
 * @throws Will catch and handle any errors during execution
 *
 * @remarks
 * - The pot requires 5 valid food ingredients to create a magical meal
 * - Golden foods are not accepted as ingredients
 * - Each ingredient type can only be used once
 * - The pot tracks ingredient count using the 'ptd_lmhc:ingredient_count' property
 * - When full, the pot creates a waffle item and launches it toward the player
 * - Special trophy recipe requires specific golden items and all achievements unlocked
 */
export async function LuckyPotInteract(LuckyPot: Entity, player: Player, item: ItemStack | undefined) {
  try {
    const dimension = LuckyPot.dimension;
    const location = LuckyPot.location;

    // Initialize/reset properties if they don't exist
    const usedIngredientsStr = LuckyPot.getDynamicProperty(USED_INGREDIENTS_PROPERTY) as string;
    if (!usedIngredientsStr) {
      resetPotProperties(LuckyPot);
    }

    // Special case for trophy ingredients - they're exempt from the golden food restriction
    const isTrophyIngredient = item && trophyIngredients.has(item.typeId);

    // Check if item is a golden food first (unless it's a trophy ingredient)
    if (item && !isTrophyIngredient && expensiveGoldenFoods.has(item.typeId)) {
      player.sendMessage('§cGolden foods are too expensive!');
      dimension.playSound('pot.error', location, { volume: 1 });
      return;
    }

    // Validate that the item is a valid food item or trophy ingredient
    if (!item || (!validFoodItems.has(item.typeId) && !isTrophyIngredient)) {
      player.sendMessage('§cYou must use a valid food item!');
      dimension.playSound('pot.error', location, { volume: 1 });
      return;
    }

    // Check if the pot is already giving food
    if (LuckyPot.getProperty('ptd_lmhc:give_food') as boolean) {
      player.sendMessage('Please wait...');
      return;
    }

    try {
      // Get current ingredient count and used ingredients
      const currentCount = LuckyPot.getProperty('ptd_lmhc:ingredient_count') as number;
      const usedIngredients = usedIngredientsStr ? (JSON.parse(usedIngredientsStr) as string[]) : [];

      // Check if this ingredient type has already been used
      if (item && usedIngredients.includes(item.type.id)) {
        player.sendMessage('§cYou cannot use the same ingredient twice!');
        dimension.playSound('pot.error', location, { volume: 1 });
        return;
      }

      // Add the new ingredient to the used ingredients list
      if (item) {
        usedIngredients.push(item.typeId);
        LuckyPot.setDynamicProperty(USED_INGREDIENTS_PROPERTY, JSON.stringify(usedIngredients));
      }

      // Check if this is potentially part of a trophy recipe
      if (item && isTrophyIngredient) {
        // Store trophy ingredients separately
        const trophyIngredientsStr = LuckyPot.getDynamicProperty(TROPHY_INGREDIENTS_PROPERTY) as string;
        const trophyIngredientsList: StoredIngredient[] = trophyIngredientsStr
          ? JSON.parse(trophyIngredientsStr)
          : [];

        trophyIngredientsList.push({
          typeId: item.typeId,
          amount: 1
        });

        LuckyPot.setDynamicProperty(TROPHY_INGREDIENTS_PROPERTY, JSON.stringify(trophyIngredientsList));
      } else if (item && currentCount < 4) {
        // Get and update stored ingredients for regular recipe items
        const storedIngredientsStr = LuckyPot.getDynamicProperty(STORED_INGREDIENTS_PROPERTY) as string;
        const storedIngredients: StoredIngredient[] = storedIngredientsStr
          ? JSON.parse(storedIngredientsStr)
          : [];

        storedIngredients.push({
          typeId: item.typeId,
          amount: 1 // We're only adding one at a time
        });

        LuckyPot.setDynamicProperty(STORED_INGREDIENTS_PROPERTY, JSON.stringify(storedIngredients));
      }

      // Increment ingredient count
      const newCount = currentCount + 1;
      LuckyPot.setProperty('ptd_lmhc:ingredient_count', newCount);
      player.sendMessage(`§aIngredients: ${newCount}/5`);
      LuckyPot.triggerEvent('ptd_lmhc:add_ingredient');
      LuckyPot.playAnimation('animation.lucky_pot.insert', { blendOutTime: 0.1 });
      const mainHandSlot = (
        player.getComponent(EntityComponentTypes.Equippable) as EntityEquippableComponent
      ).getEquipmentSlot(EquipmentSlot.Mainhand);

      // Decrement the item stack (including the fifth item)
      if (item) {
        decrementStackMainHand(mainHandSlot, item);
      }

      // Play ingredient add sound
      dimension.playSound('pot.insert', location, { volume: 1 });

      // If we've reached 5 ingredients, give food or trophy
      if (newCount >= 5) {
        // Check if the ingredients match the trophy recipe
        if (isTrophyRecipe(usedIngredients)) {
          // Check if player has unlocked all achievements
          if (hasUnlockedAllAchievements(player)) {
            // Award trophy and play special effects
            player.sendMessage('§6You have crafted the Lucky Food Trophy!');
            dimension.playSound('pot.give_food', location, { volume: 3, pitch: 1.5 });

            // Create trophy item
            const trophyItem = new ItemStack('ptd_lmhc:lucky_food_trophy');

            // Calculate spawn position slightly above the pot
            const spawnPos: Vector3 = {
              x: LuckyPot.location.x,
              y: LuckyPot.location.y + 1,
              z: LuckyPot.location.z
            };

            // Calculate direction vector from pot to player
            const directionToPlayer: Vector3 = {
              x: player.location.x - spawnPos.x,
              y: player.location.y - spawnPos.y,
              z: player.location.z - spawnPos.z
            };

            // Normalize the direction vector based on distance
            const distance = getDistance(spawnPos, player.location);
            const normalizedDirection: Vector3 = {
              x: (directionToPlayer.x / distance) * 0.15,
              y: 0.15,
              z: (directionToPlayer.z / distance) * 0.15
            };

            // Spawn the trophy with velocity
            dimension.spawnItem(trophyItem, spawnPos).applyImpulse(normalizedDirection);

            // Reset the pot
            resetPotProperties(LuckyPot);
            return;
          } else {
            // Player hasn't unlocked all achievements
            player.sendMessage('§cYou must unlock all achievements before crafting the trophy!');
            dimension.playSound('pot.error', location, { volume: 1 });

            // Return trophy ingredients to player
            returnTrophyIngredientsToPlayer(LuckyPot, player);
            return;
          }
        }

        // Regular meal creation logic (not a trophy)
        // Play effects
        player.sendMessage('§aYour meal is ready!');
        dimension.playSound('pot.give_food', location, { volume: 3 });

        // Clear stored ingredients as they've been "used" to make the meal
        LuckyPot.setDynamicProperty(STORED_INGREDIENTS_PROPERTY, '[]');
        LuckyPot.setDynamicProperty(TROPHY_INGREDIENTS_PROPERTY, '[]');

        // play the give food animation
        LuckyPot.triggerEvent('ptd_lmhc:give_food');

        // Subscribe to the give food event
        const giveFood = world.afterEvents.dataDrivenEntityTrigger.subscribe((ev) => {
          if (ev.entity.id === LuckyPot.id && ev.eventId === 'ptd_lmhc:on_give_food') {
            try {
              // Calculate spawn position slightly above the pot
              const spawnPos: Vector3 = {
                x: LuckyPot.location.x,
                y: LuckyPot.location.y + 1,
                z: LuckyPot.location.z
              };

              // Calculate direction vector from pot to player
              const directionToPlayer: Vector3 = {
                x: player.location.x - spawnPos.x,
                y: player.location.y - spawnPos.y,
                z: player.location.z - spawnPos.z
              };

              // Normalize the direction vector based on distance
              const distance = getDistance(spawnPos, player.location);
              const normalizedDirection: Vector3 = {
                x: (directionToPlayer.x / distance) * 0.15,
                y: 0.15,
                z: (directionToPlayer.z / distance) * 0.15
              };

              // Determine how many meals to give (1-3)
              const mealCount: number = Math.floor(Math.random() * 3) + 1;

              // Notify player of how many meals they're receiving
              player.sendMessage(`§6You received ${mealCount} lucky meal${mealCount > 1 ? 's' : ''}!`);

              // Get all meals from the luckyMeals set
              const meals = Array.from(luckyMeals);

              // Spawn the determined number of random meals
              for (let i = 0; i < mealCount; i++) {
                // Get a random meal for each item
                const randomMeal = meals[Math.floor(Math.random() * meals.length)] ?? 'ptd_lmhc:waffle';

                // Create slight variation in direction for multiple items
                const variedDirection: Vector3 = {
                  x: normalizedDirection.x + (Math.random() * 0.05 - 0.025),
                  y: normalizedDirection.y + Math.random() * 0.05,
                  z: normalizedDirection.z + (Math.random() * 0.05 - 0.025)
                };

                // Spawn the random meal item with velocity
                const mealItem = new ItemStack(randomMeal);
                dimension.spawnItem(mealItem, spawnPos).applyImpulse(variedDirection);
              }

              // Reset pot properties after successful meal creation
              resetPotProperties(LuckyPot);
            } catch (error) {
              // Ensure properties are reset even if there's an error
              resetPotProperties(LuckyPot);
            } finally {
              // Always unsubscribe from the event
              world.afterEvents.dataDrivenEntityTrigger.unsubscribe(giveFood);
            }
          }
        });
      }
    } catch (error) {
      // Reset properties if there's an error during interaction
      resetPotProperties(LuckyPot);
      player.sendMessage('§cSomething went wrong with the pot!');
    }
  } catch (error) {
    // Attempt to reset properties even in case of critical error
    try {
      resetPotProperties(LuckyPot);
    } catch { }
  }
}

/**
 * Handles the destruction of a Lucky Pot entity.
 * The pot must be hit 7 times before it can be collected as a spawn egg.
 * If the pot is broken before 5 ingredients are added, it returns stored ingredients.
 *
 * @param pot - The Lucky Pot entity being destroyed
 * @param player - The player destroying the pot
 *
 * @remarks
 * - Tracks destruction progress using the 'ptd_lmhc:times_to_destroy' property
 * - When fully destroyed, converts into a spawn egg and launches toward the player
 * - Plays a swing animation on each hit
 * - Returns unused ingredients if broken before completing a meal
 * - Cleans up dynamic properties on destruction
 */
export function destroyLuckyPot(pot: Entity, player: Player): void {
  try {
    const timesToDestroy = pot.getProperty('ptd_lmhc:times_to_destroy') as number;
    const spawnPos = pot.location;

    // Play the swing animation
    pot.playAnimation('animation.lucky_pot.swing', { blendOutTime: 0.1 });

    // Handle destroy lucky pot
    if (timesToDestroy >= 7) {
      // Stop sounds when the pot is actually being destroyed
      pot.runCommand('stopsound @a[r=11] pot.idle_1');

      try {
        const dimension = pot.dimension;
        const currentCount = pot.getProperty('ptd_lmhc:ingredient_count') as number;

        // Remove light block at pot location
        try {
          const blockPos = {
            x: Math.floor(pot.location.x),
            y: Math.floor(pot.location.y),
            z: Math.floor(pot.location.z)
          };
          const block = dimension.getBlock(blockPos);
          if (block && block.typeId === 'minecraft:light_block_12') {
            block.setType('minecraft:air');
          }
        } catch (error) {
        }

        // If pot was broken before getting 5 ingredients, return the stored ingredients
        if (currentCount < 5) {
          // Check for trophy ingredients first
          const trophyIngredientsStr = pot.getDynamicProperty(TROPHY_INGREDIENTS_PROPERTY) as string;
          if (trophyIngredientsStr && trophyIngredientsStr !== '[]') {
            const trophyIngredients: StoredIngredient[] = JSON.parse(trophyIngredientsStr);

            // Calculate direction for spawned items to go toward player
            const directionToPlayer: Vector3 = {
              x: player.location.x - spawnPos.x,
              y: player.location.y - spawnPos.y,
              z: player.location.z - spawnPos.z
            };

            // Normalize the direction vector based on distance
            const distance = getDistance(spawnPos, player.location);
            const normalizedDirection: Vector3 = {
              x: (directionToPlayer.x / distance) * 0.1,
              y: 0.15,
              z: (directionToPlayer.z / distance) * 0.1
            };

            // Return each trophy ingredient
            for (const ingredient of trophyIngredients) {
              const itemStack = new ItemStack(ingredient.typeId, ingredient.amount);
              dimension.spawnItem(itemStack, spawnPos).applyImpulse(normalizedDirection);
            }

            // Play return sound
            dimension.playSound('pot.error', spawnPos, { volume: 1 });
          } else {
            // Return regular stored ingredients if no trophy ingredients
            const storedIngredientsStr = pot.getDynamicProperty(STORED_INGREDIENTS_PROPERTY) as string;

            if (storedIngredientsStr) {
              const storedIngredients: StoredIngredient[] = JSON.parse(storedIngredientsStr);

              // Calculate direction for spawned items to go toward player
              const directionToPlayer: Vector3 = {
                x: player.location.x - spawnPos.x,
                y: player.location.y - spawnPos.y,
                z: player.location.z - spawnPos.z
              };

              // Normalize the direction vector based on distance
              const distance = getDistance(spawnPos, player.location);
              const normalizedDirection: Vector3 = {
                x: (directionToPlayer.x / distance) * 0.1,
                y: 0.15,
                z: (directionToPlayer.z / distance) * 0.1
              };

              // Return each stored ingredient
              for (const ingredient of storedIngredients) {
                const itemStack = new ItemStack(ingredient.typeId, ingredient.amount);
                dimension.spawnItem(itemStack, spawnPos).applyImpulse(normalizedDirection);
              }

              // Play return sound
              dimension.playSound('pot.error', spawnPos, { volume: 1 });
            }
          }
        }

        // Always clean up dynamic properties before destruction, even if returning ingredients fails
        resetPotProperties(pot);

        // Calculate direction vector from pot to player
        const directionToPlayer: Vector3 = {
          x: player.location.x - spawnPos.x,
          y: player.location.y - spawnPos.y,
          z: player.location.z - spawnPos.z
        };

        // Normalize the direction vector based on distance
        const distance = getDistance(spawnPos, player.location);
        const normalizedDirection: Vector3 = {
          x: (directionToPlayer.x / distance) * 0.1,
          y: 0.15,
          z: (directionToPlayer.z / distance) * 0.1
        };

        // Spawn the pot spawn egg
        const potSpawnEgg = new ItemStack('ptd_lmhc:lucky_pot_spawn_egg');
        pot.dimension.spawnItem(potSpawnEgg, spawnPos).applyImpulse(normalizedDirection);

        // Remove the pot
        pot.remove();
      } catch (error) {
        // Ensure properties are reset even if destruction fails
        resetPotProperties(pot);
        // Still try to remove the pot
        try {
          pot.remove();
        } catch { }
      }
    }
  } catch (error) {
    // Try one last time to clean up
    try {
      resetPotProperties(pot);
      pot.remove();
    } catch { }
  }
}
