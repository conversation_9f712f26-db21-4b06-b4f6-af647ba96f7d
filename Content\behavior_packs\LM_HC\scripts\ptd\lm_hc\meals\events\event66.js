import { getRandomLocation } from '../../utilities/vector3';
import { system } from '@minecraft/server';
/**
 * Constants for Arrow Barrage effect
 */
const ARROW_BARRAGE_DURATION_SECONDS = 15;
const ARROW_SPAWN_INTERVAL_TICKS = 5;
const ARROWS_PER_SPAWN = 2;
const MAX_HORIZONTAL_OFFSET = 10;
const ARROW_HEIGHT = 25; // Height from which arrows fall
const TOTAL_CYCLES = (ARROW_BARRAGE_DURATION_SECONDS * 20) / ARROW_SPAWN_INTERVAL_TICKS;
/**
 * Event 66: Arrow Barrage - Summons falling arrows from the sky at the player's position for 15 seconds
 * Creates a dangerous but visually striking effect of numerous arrows raining down on the area
 *
 * @param player - The player who triggered the event
 */
export function event66(player) {
    try {
        const dimension = player.dimension;
        const playerPos = player.location;
        // Counter to track how many cycles have occurred
        let cycleCount = 0;
        // Store interval ID for cleanup
        let intervalId;
        // Begin the arrow barrage
        intervalId = system.runInterval(() => {
            // Check if player is still valid
            if (!player) {
                system.clearRun(intervalId);
                return;
            }
            // Stop when duration is reached
            if (cycleCount >= TOTAL_CYCLES) {
                system.clearRun(intervalId);
                return;
            }
            // Spawn multiple arrows each cycle
            for (let i = 0; i < ARROWS_PER_SPAWN; i++) {
                // Get a random position above the player
                const spawnPos = getRandomLocation(playerPos, dimension, 1, // Base offset
                MAX_HORIZONTAL_OFFSET, // Additional random offset
                ARROW_HEIGHT, // Height above player
                false // No need to check for air blocks for arrows
                );
                if (spawnPos) {
                    // Spawn the arrow
                    const arrow = dimension.spawnEntity('minecraft:arrow', spawnPos);
                    // Apply randomized velocity for natural falling pattern
                    if (arrow) {
                        // Optional: Make arrows more damaging
                        // This could be done through NBT data if needed
                        // Play particle effect at spawn location
                        dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);
                        // Only play sound occasionally to avoid overwhelming sound
                        if (Math.random() < 0.2) {
                            dimension.playSound('mob.endermen.portal', spawnPos);
                        }
                    }
                }
            }
            // Increment cycle counter
            cycleCount++;
        }, ARROW_SPAWN_INTERVAL_TICKS);
        // Safety cleanup in case something goes wrong
        system.runTimeout(() => {
            if (intervalId) {
                system.clearRun(intervalId);
            }
        }, (ARROW_BARRAGE_DURATION_SECONDS + 1) * 20); // Add 1 second buffer
    }
    catch (error) {
    }
    return;
}
