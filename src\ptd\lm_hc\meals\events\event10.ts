import { Player, Vector3 } from '@minecraft/server';
import { getRandomInt } from '../../utilities/rng';
import { getRandomLocation } from '../../utilities/vector3';
import { EntityQuantityConfig, spawnEntitiesWithInterval } from '../../utilities/summonEntity';

/**
 * Event 10: Zombie Swarm - Spawns zombies rising from the ground around the player,
 * positioned exactly 1 block above solid ground with randomized Y-level variations
 *
 * @param player - The player who triggered the event
 */
export function event10(player: Player): void {
  try {
    // Configure zombie entity type and quantity
    const entityConfigs: EntityQuantityConfig[] = [{ entityId: 'ptd_lmhc:zombie', count: 50 }];

    /**
     * Finds a valid spawn position that is exactly 1 block above a solid block,
     * with additional randomized Y-offset adjustments
     *
     * @returns Valid spawn Vector3 position or undefined if none found
     */
    const getSpawnLocation = (): Vector3 | undefined => {
      // Set radius range for horizontal search
      const minRadius: number = 3;
      const maxRadius: number = 10;

      // Get a random location near the player (only for X and Z coordinates)
      const randomLocation = getRandomLocation(
        player.location,
        player.dimension,
        minRadius,
        getRandomInt(0, maxRadius - minRadius),
        0, // Set Y offset to 0, we'll handle this manually
        true // Check for air blocks
      );

      if (!randomLocation) return undefined;

      // Get the y-coordinate to start searching from (slightly randomized)
      const searchStartY: number = player.location.y + getRandomInt(-5, 5);

      // Check up to 15 blocks down from the search start position
      for (let yOffset = 0; yOffset >= -15; yOffset--) {
        const checkPos: Vector3 = {
          x: randomLocation.x,
          y: searchStartY + yOffset,
          z: randomLocation.z
        };

        // Get the block at this position
        const currentBlock = player.dimension.getBlock(checkPos);
        if (!currentBlock) continue;

        // If we found a non-air block
        if (!currentBlock.isAir) {
          // Check if the block above is air (for spawning)
          const spawnPos: Vector3 = {
            x: randomLocation.x,
            y: checkPos.y + 1.1, // Exactly 1.1 blocks above solid ground
            z: randomLocation.z
          };

          const spawnBlock = player.dimension.getBlock(spawnPos);

          // If spawn position is air, this is a valid location
          if (spawnBlock && spawnBlock.isAir) {
            // Play cauldron explosion particle effect at spawn location
            player.dimension.spawnParticle('minecraft:cauldron_explosion_particle', {
              x: spawnPos.x,
              y: spawnPos.y - 0.5, // Slightly below spawn position
              z: spawnPos.z
            });
            player.dimension.playSound('mob.endermen.portal', spawnPos);

            return spawnPos;
          }
        }
      }

      return undefined;
    };

    // Start spawning zombies with a 5 tick delay between each
    spawnEntitiesWithInterval(
      player.dimension,
      entityConfigs,
      getSpawnLocation,
      5, // Same delay as original implementation
      (zombie) => {
        // Optional zombie configuration after spawn
        if (zombie && zombie) {
          // Trigger appropriate zombie rise animation if needed
          zombie.triggerEvent('minecraft:entity_spawned');
        }
      }
    ).catch((error) => {
    });
  } catch (error) {
  }
  return;
}
