// Lucky Horse: Spawns a pre-tamed horse with diamond armor
import { EntityComponentTypes, ItemStack, system, EffectTypes } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';
import { getRandomInt } from '../../utilities/rng';
/**
 * Available horse variant events that can be triggered
 */
const HORSE_VARIANT_EVENTS = [
    'minecraft:make_white',
    'minecraft:make_creamy',
    'minecraft:make_chestnut',
    'minecraft:make_brown',
    'minecraft:make_black',
    'minecraft:make_gray',
    'minecraft:make_darkbrown'
];
/**
 * Event 49: Lucky Horse
 * Spawns a pre-tamed horse already equipped with diamond armor
 * @param player The player who triggered the event
 */
export async function event49(player) {
    try {
        // Find a suitable location to spawn the horse (with air blocks)
        const spawnPos = getRandomLocation(player.location, player.dimension, 3, // base offset
        2, // additional offset
        0, // vertical offset
        true // check for air blocks
        );
        if (!spawnPos) {
            return;
        }
        // Spawn the horse as an adult
        const horse = player.dimension.spawnEntity('minecraft:horse<minecraft:spawn_adult>', spawnPos);
        // Visual and sound effects
        player.dimension.playSound('mob.endermen.portal', spawnPos);
        player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);
        if (!horse) {
            return;
        }
        // Name the horse
        horse.nameTag = `${player.name}'s Lucky Horse`;
        // Randomly set horse variant
        const variantIndex = getRandomInt(0, HORSE_VARIANT_EVENTS.length - 1);
        const variantEvent = HORSE_VARIANT_EVENTS[variantIndex] ?? HORSE_VARIANT_EVENTS[0];
        horse.triggerEvent(variantEvent);
        // Apply special effects to make the horse faster and jump higher
        // Duration set to a very large number for permanent effects (24000 ticks = 20 minutes)
        const speedEffect = EffectTypes.get('speed');
        if (speedEffect) {
            horse.addEffect(speedEffect, 24000, {
                amplifier: 1, // Speed II
                showParticles: false
            });
        }
        else {
            // Fallback using string identifier if EffectTypes.get() fails
            horse.addEffect('speed', 24000, {
                amplifier: 1,
                showParticles: false
            });
        }
        const jumpBoostEffect = EffectTypes.get('jump_boost');
        if (jumpBoostEffect) {
            horse.addEffect(jumpBoostEffect, 24000, {
                amplifier: 2, // Jump Boost III
                showParticles: false
            });
        }
        else {
            // Fallback using string identifier if EffectTypes.get() fails
            horse.addEffect('jump_boost', 24000, {
                amplifier: 2,
                showParticles: false
            });
        }
        // Tame the horse to the player
        const tameComponent = horse.getComponent(EntityComponentTypes.TameMount);
        if (tameComponent) {
            tameComponent.tameToPlayer(true, player);
        }
        // Add diamond horse armor after waiting a tick
        await system.waitTicks(1);
        try {
            // Get the horse's inventory component
            const inventory = horse.getComponent(EntityComponentTypes.Inventory);
            if (inventory?.container) {
                // Create diamond horse armor item
                const diamondArmor = new ItemStack('minecraft:diamond_horse_armor', 1);
                // Equip the armor (slot 1 is for horse armor)
                inventory.container.setItem(1, diamondArmor);
                // Also add a saddle so the player can ride immediately
                const saddle = new ItemStack('minecraft:saddle', 1);
                // Equip the saddle (slot 0 is for saddle)
                inventory.container.setItem(0, saddle);
            }
        }
        catch (error) {
        }
    }
    catch (error) {
    }
    return;
}
