import { ItemStack, system } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';
import { getRandomInt } from '../../utilities/rng';
/**
 * Event 43: Lucky Cow
 * Spawns a mushroom cow (mooshroom) that drops golden apples when milked
 * The cow is named "Lucky Cow" in gold font
 * The player can only interact with the cow once to get golden apples
 *
 * @param player - The player to spawn the lucky cow near
 */
export function event43(player) {
    try {
        const location = getRandomLocation(player.location, player.dimension, 1, // base offset
        getRandomInt(-5, 5), // additional horizontal offset
        0, // Y offset (spawn at ground level)
        true // check for air block
        );
        if (location) {
            // Spawn the lucky cow (mooshroom)
            const luckyCow = player.dimension.spawnEntity('minecraft:mooshroom', location);
            luckyCow.nameTag = '§6Lucky Cow§r';
            // Play spawn effects
            player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', location);
            player.dimension.playSound('mob.endermen.portal', location);
        }
    }
    catch (error) {
    }
    return;
}
/**
 * Handles the interaction with a Lucky Cow
 * Spawns 4-16 golden apples when the player interacts with the cow
 * The cow's name is removed after interaction to indicate it can't be milked again
 *
 * @param player - The player interacting with the cow
 * @param entity - The Lucky Cow entity being interacted with
 * @returns boolean - True if the interaction was handled, false otherwise
 */
export function handleLuckyCowInteraction(player, entity) {
    try {
        // Check if this is a lucky cow by its name tag
        if (entity.nameTag === '§6Lucky Cow§r') {
            // Determine how many golden apples to spawn (4-16)
            const appleCount = getRandomInt(4, 16);
            let spawned = 0;
            // Get the position slightly above the cow
            const spawnPos = {
                x: entity.location.x,
                y: entity.location.y + 1.5,
                z: entity.location.z
            };
            // Spawn golden apples with a small delay between each one
            const spawnInterval = system.runInterval(() => {
                try {
                    // Stop the interval once we've spawned all apples
                    if (spawned >= appleCount) {
                        system.clearRun(spawnInterval);
                        // Play despawn effects
                        player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', entity.location);
                        player.dimension.playSound('mob.endermen.portal', entity.location);
                        entity.remove();
                        return;
                    }
                    // Create and spawn a golden apple item stack
                    const goldenApple = new ItemStack('minecraft:golden_apple', 1);
                    player.dimension.spawnItem(goldenApple, spawnPos);
                    // Add some randomness to the spawn position for the next apple
                    spawnPos.x += (Math.random() - 0.5) * 0.5;
                    spawnPos.z += (Math.random() - 0.5) * 0.5;
                    // Play effects
                    player.dimension.spawnParticle('minecraft:villager_happy', spawnPos);
                    spawned++;
                }
                catch (error) {
                    system.clearRun(spawnInterval);
                }
            }, 5); // Run every 5 ticks (0.25 seconds) for smoother spawning
            // Send a message to the player
            player.sendMessage('§6The Lucky Cow has blessed you with golden apples!§r');
            return true;
        }
        // If the cow has an empty name tag (was previously a Lucky Cow)
        if (entity.typeId === 'minecraft:mooshroom' && entity.nameTag === '') {
            player.sendMessage('§7This cow has already been milked and lost its magic.§r');
            return true;
        }
        return false;
    }
    catch (error) {
        return false;
    }
}
