import { getRandomLocation } from '../../utilities/vector3';
/**
 * Event 7. Mystic Trader: Spawns a wandering trader with extremely valuable trades
 * The trader offers powerful items like Netherite equipment for just 1 emerald
 *
 * Finds a safe air block near the player to spawn the trader
 * Base offset: 2 blocks
 * Additional random offset: 1 block
 * Y offset: 0 blocks (same level as player)
 *
 * @param player - The player near whom to spawn the mystic trader
 * @throws Will throw if unable to access player dimension or location, or if no safe spawn location is found
 */
export function event7(player) {
    try {
        const dimension = player.dimension;
        const playerPos = player.location;
        // Find a safe location near the player with air block validation
        const spawnPos = getRandomLocation(playerPos, dimension, 2, 1, 0, true);
        if (!spawnPos) {
            return;
        }
        // Spawn the special wandering trader with OP trades at the safe location
        // Spawn the special wandering trader with custom name tag
        const mysticTrader = dimension.spawnEntity('ptd_lmhc:mystic_trader', spawnPos);
        dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);
        dimension.playSound('mob.endermen.portal', spawnPos);
        // Set a colored name tag for the mystic trader (using Minecraft's formatting codes)
        // §5 = Purple, §d = Light Purple, §e = Yellow, §b = Aqua
        mysticTrader.nameTag = '§5✦ §dM§ey§bs§dt§ei§bc §dT§er§ba§dd§ee§br §5✦§r';
    }
    catch (error) {
    }
    return;
}
