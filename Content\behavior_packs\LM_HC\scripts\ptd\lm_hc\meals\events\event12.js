import { system } from '@minecraft/server';
import { getRandomInt } from '../../utilities/rng';
import { PROTECTED_BLOCKS } from '../../utilities/constants/protectedBlocks';
/**
 * Event 12: Lava Pool - Creates a dangerous lava pool around the player's location.
 * The lava blocks are placed in a circular pattern around the player, creating a hazardous situation.
 *
 * @param player - The player entity around which the lava will appear
 * @throws Will log a warning message if the event execution fails
 *
 * @remarks
 * - Creates a circular lava pool around the player
 * - Places lava blocks in a radius of 2-4 blocks from the player
 * - Spawns lava gradually over a short period for dramatic effect
 * - Plays sound and particle effects for immersion
 */
export function event12(player) {
    try {
        const startPosition = player.location;
        const dimension = player.dimension;
        // Play warning sound
        dimension.runCommand(`playsound random.fizz @p[name="${player.name}"] ~~~ 1.0 0.5`);
        // Spawn parameters
        const radius = 12; // Maximum radius of lava pool (increased from 4)
        const maxLavaBlocks = 128; // Total number of lava blocks to place (increased from 35)
        // Generate positions in a circle around the player
        const lavaPositions = [];
        // Create a more uniform pattern of positions to make a continuous pool
        // First, create concentric rings of lava
        const rings = 8; // Number of rings
        const blocksPerRing = Math.floor(maxLavaBlocks / rings);
        for (let ring = 0; ring < rings; ring++) {
            const ringRadius = 2 + ring; // Start from radius 2 and increase
            for (let i = 0; i < blocksPerRing; i++) {
                const angle = (i / blocksPerRing) * Math.PI * 2;
                const offsetX = Math.sin(angle) * ringRadius;
                const offsetZ = Math.cos(angle) * ringRadius;
                lavaPositions.push({
                    x: Math.floor(startPosition.x + offsetX),
                    y: Math.floor(startPosition.y - 1), // Place at feet level
                    z: Math.floor(startPosition.z + offsetZ)
                });
            }
        }
        // Fill in gaps with additional blocks
        const remainingBlocks = maxLavaBlocks - lavaPositions.length;
        for (let i = 0; i < remainingBlocks; i++) {
            const angle = (i / remainingBlocks) * Math.PI * 2;
            const distance = getRandomInt(2, radius - 1); // Slightly reduced radius for gap filling
            const offsetX = Math.sin(angle) * distance;
            const offsetZ = Math.cos(angle) * distance;
            lavaPositions.push({
                x: Math.floor(startPosition.x + offsetX),
                y: Math.floor(startPosition.y - 1),
                z: Math.floor(startPosition.z + offsetZ)
            });
        }
        // Spawn lava gradually for dramatic effect
        let spawnedCount = 0;
        const intervalId = system.runInterval(() => {
            try {
                // Exit condition - all lava blocks placed
                if (spawnedCount >= lavaPositions.length) {
                    system.clearRun(intervalId);
                    return;
                }
                // Using boundary checking to ensure we don't access undefined values
                if (spawnedCount < 0 || spawnedCount >= lavaPositions.length) {
                    system.clearRun(intervalId);
                    return;
                }
                // Get the current position with proper array bounds checking
                // We know this is safe because of the boundary check above
                const currentPosition = lavaPositions[spawnedCount];
                // Check if the block at this position is protected before placing lava
                const blockPos = { x: currentPosition.x, y: currentPosition.y, z: currentPosition.z };
                const block = dimension.getBlock(blockPos);
                // Only place lava if the block is not in our protected list
                if (block && !PROTECTED_BLOCKS.has(block.typeId)) {
                    // Place lava block and add visual/sound effects
                    dimension.runCommand(`setblock ${currentPosition.x} ${currentPosition.y} ${currentPosition.z} lava`);
                }
                dimension.spawnParticle('minecraft:lava_particle', {
                    x: currentPosition.x,
                    y: currentPosition.y + 1,
                    z: currentPosition.z
                });
                // Play random sounds occasionally
                if (spawnedCount % 3 === 0) {
                    dimension.runCommand(`playsound lava.pop @p[name="${player.name}"] ${currentPosition.x} ${currentPosition.y} ${currentPosition.z}`);
                }
                spawnedCount++;
            }
            catch (error) {
                system.clearRun(intervalId);
                return;
            }
        }, 1); // Run every tick for rapid lava spread
        return; // Ensure function exits properly
    }
    catch (error) {
        return; // Ensure function exits properly
    }
}
