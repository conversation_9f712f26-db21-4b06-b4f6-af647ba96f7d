// RANDOM NUMBER GENERATOR UTILITIES

/**
 * Generates a random integer between min and max (inclusive)
 * @param min The minimum value (inclusive)
 * @param max The maximum value (inclusive)
 * @returns A random integer between min and max
 * @throws Error if min is greater than max
 */
export function getRandomInt(min: number, max: number): number {
  // Ensure parameters are integers
  min = Math.ceil(min);
  max = Math.floor(max);

  // Validate input
  if (min > max) {
  }

  return Math.floor(Math.random() * (max - min + 1)) + min;
}
