import { BlockPermutation, Player, Vector3 } from '@minecraft/server';
import { system } from '@minecraft/server';
import { PROTECTED_BLOCKS } from '../../utilities/constants/protectedBlocks';

/**
 * Event 65: Cake Chamber - Surrounds the player in a Cake wall
 * Creates a 5x5x5 cube of cake blocks with the player in the center
 *
 * @param player - The player who triggered the event
 */
export function event65(player: Player): void {
  try {
    // Get the cake block permutation
    const cakePermutation: BlockPermutation = BlockPermutation.resolve('minecraft:cake');

    // Get player's position rounded to whole blocks
    const playerPosition: Vector3 = {
      x: Math.floor(player.location.x),
      y: Math.floor(player.location.y),
      z: Math.floor(player.location.z)
    };

    // Define the size of the cake cube
    const size: number = 2; // This creates a 5x5x5 cube (2 blocks in each direction + player's position)

    // Use system.run for better performance when placing multiple blocks
    system.run(() => {
      // Loop through all possible positions to create the cake chamber
      for (let x = -size; x <= size; x++) {
        for (let y = -size; y <= size; y++) {
          for (let z = -size; z <= size; z++) {
            // Only place blocks on the outer layer (creates a hollow cube)
            if (Math.abs(x) === size || Math.abs(y) === size || Math.abs(z) === size) {
              const blockPos: Vector3 = {
                x: playerPosition.x + x,
                y: playerPosition.y + y,
                z: playerPosition.z + z
              };

              try {
                // Get the current block
                const block = player.dimension.getBlock(blockPos);

                // Skip if block doesn't exist
                if (!block) continue;

                // Skip if it's where the player is standing
                if (x === 0 && y === 0 && z === 0) continue;

                // Skip if the block is a protected block
                if (PROTECTED_BLOCKS.has(block.type.id)) continue;

                // Play particle effect before placing the block
                player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', blockPos);

                // Place the cake block
                block.setPermutation(cakePermutation);

                // Play sound effect when placing blocks
                if (Math.random() < 0.1) {
                  // Only play sound for 10% of the blocks to avoid sound spam
                  player.dimension.playSound('mob.endermen.portal', blockPos);
                }
              } catch (blockError) {
                // Log any errors with specific block placement
              }
            }
          }
        }
      }
    });
  } catch (error) {
  }
  return;
}
