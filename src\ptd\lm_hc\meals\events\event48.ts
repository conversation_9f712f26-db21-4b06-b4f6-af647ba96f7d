import { Player, system } from '@minecraft/server';

/**
 * Event 48: Lightning Storm
 * Continuous lightning strikes hit the player every 2 seconds for 10 seconds
 * @param player The player who triggered the event
 */
export function event48(player: Player): void {
  try {
    const strikeCount = 5; // 5 strikes total (one every 2 seconds for 10 seconds)
    let strikesCompleted = 0;

    const lightningInterval = system.runInterval(() => {
      if (strikesCompleted >= strikeCount) {
        system.clearRun(lightningInterval);
        return;
      }

      try {
        // Strike lightning at the player's location
        player.dimension.spawnEntity('minecraft:lightning_bolt', player.location);

        // Display particle effects for visual feedback
        player.dimension.spawnParticle('minecraft:huge_explosion_emitter', player.location);

        strikesCompleted++;
      } catch (error) {
      }
    }, 40); // 40 ticks = 2 seconds
  } catch (error) {
  }
  return;
}
