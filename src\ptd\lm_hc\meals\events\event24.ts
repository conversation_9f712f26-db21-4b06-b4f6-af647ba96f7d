import { Player, Vector3 } from '@minecraft/server';
import { teleport } from '../../utilities/teleport';
import { onEatMeal } from '../events';

/**
 * Event 24: Sky Drop
 * Instantly teleports the player 100 blocks into the sky
 * @param player The player who triggered the event
 */
export function event24(player: Player): void {
  try {
    let currentPos: Vector3;
    const spawnPoint = player.getSpawnPoint();
    if (spawnPoint) {
      currentPos = spawnPoint;
    } else {
      currentPos = player.location; // Fallback to player.location if spawn point is undefined
    }
    const targetPos: Vector3 = {
      x: currentPos.x,
      y: currentPos.y + 100,
      z: currentPos.z
    };
    teleport(player, targetPos);
  } catch (error) {
    onEatMeal(player);
  }
  return;
}
