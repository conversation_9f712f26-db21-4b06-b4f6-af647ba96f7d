import { Player, system, Vector3 } from "@minecraft/server";

// Dynamic property constants
export const FLIGHT_ACTIVE_PROP = "ptd_lmhc:flight_active";
export const FLIGHT_END_TIME_PROP = "ptd_lmhc:flight_end_time";
export const FLIGHT_SLOWDOWN_PROP = "ptd_lmhc:flight_slowdown";

// State management for flight mode timeouts only (intervals handled by main.ts)
export const playerFlightTimeouts = new Map<string, number>();

/**
 * Event 29: Flying Mode
 * Gives the player flight abilities for 60 seconds using adapted wings mechanics
 * @param player The player who triggered the event
 */
export function event29(player: Player): void {
  try {
    startFlightForPlayer(player);
  } catch (error) {
  }
}

/**
 * Starts flight mode for a specific player
 * @param player The player to start flight mode for
 */
export function startFlightForPlayer(player: Player): void {
  try {
    const CONFIG = {
      DURATION: 1200, // 60 seconds (20 ticks/second)
    } as const;

    const playerId: string = player.id;
    const currentTime: number = system.currentTick;
    const endTime: number = currentTime + CONFIG.DURATION;

    // Set dynamic properties for persistence across disconnections
    player.setDynamicProperty(FLIGHT_ACTIVE_PROP, true);
    player.setDynamicProperty(FLIGHT_END_TIME_PROP, endTime);
    player.setDynamicProperty(FLIGHT_SLOWDOWN_PROP, 0.05);

    player.onScreenDisplay.setActionBar(
      "§bFlying Mode activated for 60 seconds!"
    );

    // Set timeout to deactivate flight mode
    const timeoutId: number = system.runTimeout(() => {
      try {
        if (player.getDynamicProperty(FLIGHT_ACTIVE_PROP)) {
          stopFlightForPlayer(player);
        }
      } catch (error) {
        cleanupPlayerFlight(playerId);
      }
    }, CONFIG.DURATION);

    // Store timeout ID for cleanup
    playerFlightTimeouts.set(playerId, timeoutId);

  } catch (error) {
  }
}

/**
 * Stops flight mode for a specific player
 * @param player The player to stop flight mode for
 */
export function stopFlightForPlayer(player: Player): void {
  try {
    const playerId: string = player.id;

    // Clear dynamic properties
    player.setDynamicProperty(FLIGHT_ACTIVE_PROP, false);
    player.setDynamicProperty(FLIGHT_END_TIME_PROP, undefined);
    player.setDynamicProperty(FLIGHT_SLOWDOWN_PROP, undefined);

    player.onScreenDisplay.setActionBar("§cFlying Mode deactivated!");

    // Clean up timeout
    cleanupPlayerFlight(playerId);

  } catch (error) {
  }
}

/**
 * Cleans up flight-related data for a player
 * @param playerId The player ID to clean up
 */
export function cleanupPlayerFlight(playerId: string): void {
  try {
    // Clear timeout if it exists
    const timeoutId: number | undefined = playerFlightTimeouts.get(playerId);
    if (timeoutId !== undefined) {
      system.clearRun(timeoutId);
      playerFlightTimeouts.delete(playerId);
    }
  } catch (error) {
  }
}

/**
 * Main flight mechanics function adapted from wings code
 * Checks if player should have flight and applies mechanics
 * @param player The player to apply flight mechanics to
 */
export function flightMechanics(player: Player): void {

  try {
    // Check if flight is active and hasn't expired
    const isFlightActive: boolean = player.getDynamicProperty(FLIGHT_ACTIVE_PROP) as boolean;

    if (!isFlightActive) {
      return;
    }
    
    const flightEndTime: number = player.getDynamicProperty(FLIGHT_END_TIME_PROP) as number;
    const currentTime: number = system.currentTick;

    if (flightEndTime && (flightEndTime - currentTime === 200)) {
      player.sendMessage('§cYou have 10 seconds of flight time left!');
      return;
    }

    // Check if flight has expired
    if (flightEndTime && currentTime >= flightEndTime) {
      stopFlightForPlayer(player);
      return;
    }

    // Check if the player is jumping or not on the ground
    const isJumping: boolean = player.isJumping;
    const isOnGround: boolean = player.isOnGround;
    const isSneaking: boolean = player.isSneaking;
    const isSprinting: boolean = player.isSprinting;

    if (isJumping || !isOnGround) {
      applyFlightMechanics(player, isJumping, isSneaking, isSprinting);
    }
  } catch (error) {
  }
}

/**
 * Applies the core flight mechanics adapted from wings code
 * @param player The player to apply mechanics to
 * @param isJumping Whether the player is jumping
 * @param isSneaking Whether the player is sneaking
 * @param isSprinting Whether the player is sprinting
 */
function applyFlightMechanics(
  player: Player,
  isJumping: boolean,
  isSneaking: boolean,
  isSprinting: boolean
): void {
  const velocity: Vector3 = player.getVelocity();
  const viewDirection: Vector3 = player.getViewDirection();

  // Apply base flight mechanics
  if (isJumping) {
    player.addEffect("levitation", 5, { amplifier: 10, showParticles: false });
  }
  player.addEffect("slow_falling", 5, { amplifier: 1, showParticles: false });

  /**
   * Applies knockback to the player
   * @param x X direction component
   * @param z Z direction component
   * @param speed Speed multiplier
   * @param y Y direction component
   */
  const applyKnockback = (
    x: number,
    z: number,
    speed: number,
    y: number
  ): void => {
    player.applyKnockback(x, z, speed, y);
  };

  if (!isSneaking) {
    // Check if the player is moving significantly
    if (
      velocity &&
      (Math.abs(velocity.x) > 0.1 || Math.abs(velocity.z) > 0.1)
    ) {
      // Gradually increase the slowdown value, capping it at 1
      const currentSlowDown: number = (player.getDynamicProperty(FLIGHT_SLOWDOWN_PROP) as number) || 0.05;
      player.setDynamicProperty(FLIGHT_SLOWDOWN_PROP, Math.min(currentSlowDown + 0.1, 1));

      // Determine the speed multiplier based on whether the player is sprinting
      const speed: number = isSprinting ? 2.0 : 1.0;

      if (isJumping) {
        // Apply knockback with upward force if the player is jumping
        applyKnockback(viewDirection.x, viewDirection.z, speed, 0.6);
      } else {
        // Apply knockback with slight downward force if the player is not jumping
        applyKnockback(viewDirection.x, viewDirection.z, speed, -0.05);
      }
    }
  } else {
    // Retrieve the current slowdown value for the player
    const slowDown: number = (player.getDynamicProperty(FLIGHT_SLOWDOWN_PROP) as number) || 0.05;

    // Apply knockback to the player with the current slowdown value and a downward force
    player.applyKnockback(viewDirection.x, viewDirection.z, slowDown, -0.4);

    // Gradually decrease the slowdown value, ensuring it does not go below 0.05
    const currentSlowDown: number = (player.getDynamicProperty(FLIGHT_SLOWDOWN_PROP) as number) || 0.05;
    player.setDynamicProperty(FLIGHT_SLOWDOWN_PROP, Math.max(currentSlowDown - 0.03, 0.05));
  }
}

/**
 * Restores flight state for a player who has rejoined the world
 * @param player The player to restore flight state for
 */
export function restoreFlightOnSpawn(player: Player): void {
  try {
    const isFlightActive: boolean = player.getDynamicProperty(FLIGHT_ACTIVE_PROP) as boolean;
    const flightEndTime: number = player.getDynamicProperty(FLIGHT_END_TIME_PROP) as number;

    if (!isFlightActive || !flightEndTime) {
      return;
    }
    const playerId: string = player.id;

    // Set new timeout for remaining time
    const timeoutId: number = system.runTimeout(() => {
      try {
        if (player.getDynamicProperty(FLIGHT_ACTIVE_PROP)) {
          stopFlightForPlayer(player);
        }
      } catch (error) {
        cleanupPlayerFlight(playerId);
      }
    }, flightEndTime);

    // Store timeout ID for cleanup
    playerFlightTimeouts.set(playerId, timeoutId);

  } catch (error) {
  }
}
